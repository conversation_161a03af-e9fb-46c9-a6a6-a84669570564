import oracledb from 'oracledb';
import logger from '../../frameworks_drivers/middlewares/logger/log';
import { StopWatch } from '../../frameworks_drivers/middlewares/logger/stopwatch';
import ConsulConsult from '../../frameworks_drivers/middlewares/consulConsult';

export class ConfigOracleDB {

	private connection: any;
	private consul = new ConsulConsult();

	async getConnection() {
		const databaseUrl = await this.consul.consultKey(process.env.DB_CONNECTION);
		let objLogger: any = {
			operation: "BFF.CONNECT.DATABASE"
		};

		const timer = new StopWatch();
		timer.start();

		const Config =
		{
			user: process.env.NODE_DATASOURCE_ORACLE_USERNAME,
            password: process.env.NODE_DATASOURCE_ORACLE_PASSWORD,
            connectString: databaseUrl,

		}
		try {

			objLogger = {
				duration: timer.getElapsedMilliseconds()
			};

			logger.info(objLogger, "Iniciando conexão ao banco de dados");

			this.connection = await oracledb.getConnection(Config);

			objLogger.duration = timer.getElapsedMilliseconds();;
			objLogger.short_message = "success";
			logger.info(objLogger, "Conexão ao banco de dados concluida com sucesso");

			console.log(`Connected to OracleDB: ${Config.user} at ${Config.connectString}`);
			return this.connection;
		}
		catch (error) {

			objLogger["short_message"] = error;
			objLogger["duration"] = timer.getElapsedMilliseconds();
			logger.error(objLogger, "Erro ao conectar ao banco de dados ", error?.message);


			throw {
				name:"DBError",
				message: `Could not to OracleDB: ${Config.user} at ${Config.connectString}`,
				error: error
			}
		}
	}

	async getSysDateFromDual() {
		this.connection = await this.getConnection()
		const sql = `SELECT sysdate FROM dual WHERE :b = 1`;
		const binds = [1];
		const options = { outFormat: oracledb.OUT_FORMAT_OBJECT };
		const result = await this.connection.execute(sql, binds, options);
		console.log(result)
	}

	async getCidadeOrigem(branchId: any, transactioId: any) {

		let objLogger: any = {
			operation: "DATABASE.GET.CIDADE.ORIGEM",
			transactioId
		};

		const timer = new StopWatch();
		timer.start();

		try {


			logger.info(objLogger, "Iniciando consulta de cidade origem");

			this.connection = await this.getConnection()
			const sql = "select c.cd_pais, c.cd_estado, c.cd_sequencia_webservice from SYSTUR.gen_pes_cvc a, SYSTUR.GEN_PES_END b, SYSTUR.gen_cidade c "
				+ " where a.cd_pessoa = b.cd_pessoa "
				+ " and b.id_endereco = :endereco "
				+ " and b.cd_estado = c.cd_estado "
				+ " and b.cd_pais = c.cd_pais "
				+ " and b.cd_cidade = c.cd_cidade "
				+ " and a.cd_filial_cvc = :filial";
			const binds = ['C', branchId];
			const options = { outFormat: oracledb.OUT_FORMAT_OBJECT };
			const result = await this.connection.execute(sql, binds, options);

			objLogger.duration = timer.getElapsedMilliseconds();;
			objLogger.short_message = "success";
			logger.info(objLogger, "Consulta  de cidade origem concluida com sucesso");

			return result

		}
		catch (error) {

			const { message } = error;
			const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
			objLogger["short_message"] = shortMessage;
			objLogger["duration"] = timer.getElapsedMilliseconds();
			logger.error(objLogger, "Erro ao consultar a cidade de origem ", error.message);

			throw {
				name: "DBError",
				message: "When getCidadeOrigem",
				error: error
			}

		}

	}

	async getCidadeDestino(destino: any) {

		// FIX TO DESTINO WITH ,
		destino = destino.split(",")[0];

		this.connection = await this.getConnection()
		const sql = "SELECT c.cd_pais, c.cd_estado, c.cd_sequencia_webservice FROM SYSTUR.gen_cidade c "
			+ " WHERE c.NM_CIDADE = UPPER( :destino )";
		const binds = [destino];
		const options = { outFormat: oracledb.OUT_FORMAT_OBJECT };
		const result = await this.connection.execute(sql, binds, options);
		return result
	}

	async getCityInfo(city: String, state: String) {


		const sql =
			[
				"SELECT c.cd_pais, c.cd_estado, c.cd_sequencia_webservice, c.NM_CIDADE",
				"FROM SYSTUR.gen_cidade c, SYSTUR.gen_estado d",
				"WHERE c.NM_CIDADE = UPPER( :city )",
				"and d.nm_estado = UPPER( :state )",
				"and c.cd_estado = d.cd_estado"
			];

		const sqlJoined = sql.join(" ");

		let objLogger: any = {
			operation: "DATABASE.GET.CITY.INFO",
			sqlJoined
		};

		const timer = new StopWatch();
		timer.start();




		const options = { outFormat: oracledb.OUT_FORMAT_OBJECT };
		let result;


		try {

			logger.info(objLogger, "Iniciando consulta da informação da cidade");

			this.connection = await this.getConnection()

			result = await this.connection.execute
				(
					sqlJoined,
					{
						city:
						{
							dir: oracledb.BIND_INOUT,
							val: city,
							type: oracledb.STRING
						},
						state:
						{
							dir: oracledb.BIND_INOUT,
							val: state,
							type: oracledb.STRING
						}
					},
					options
				);
				
			result.query = sqlJoined + ", being :city = " + city + ", :state = " + state;

			objLogger.duration = timer.getElapsedMilliseconds();;
			objLogger.short_message = "success";
			logger.info(objLogger, "Consulta  da informação da cidade concluida com sucesso");
		}
		catch (error) {

			const { message } = error;
			const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
			objLogger["short_message"] = shortMessage;
			objLogger["duration"] = timer.getElapsedMilliseconds();
			logger.error(objLogger, "Erro ao consultar a informação da cidade ", error.message);

			throw {
				error: error,
				message: "Could not query: " + sqlJoined
			};
		};

		return result
	}

}

