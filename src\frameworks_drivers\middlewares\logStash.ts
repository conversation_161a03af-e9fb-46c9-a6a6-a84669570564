import { Injectable, LoggerService } from '@nestjs/common';
import { configure, getLogger } from "log4js";

@Injectable()
export class LogstashService implements LoggerService {
    private logger = getLogger(`[${process.env.PREFIX}]`);
    public request:any = {}

    constructor() {
        this.configureLog();
    }

    log(message: any, context?: string) {
        if(context)
            this.logger.info(this.getMeta(), message, context);
        else 
            this.logger.info(this.getMeta(), message);
    }

    error(message: any, context?: string) {
        if(context)
            this.logger.error(this.getMeta(), message, context);
        else 
            this.logger.error(this.getMeta(), message);
        
    }

    warn(message: any, context?: string) {
        if(context)
            this.logger.warn(this.getMeta(), message, context);
        else 
            this.logger.warn(this.getMeta(), message);
        
    }

    debug?(message: any, context?: string) {
        if(context)
            this.logger.debug(this.getMeta(), message, context);
        else 
            this.logger.debug(this.getMeta(), message);
    }

    // verbose?(message: any, context?: string) {
        
    // }

    private configureLog() {
        if(process.env.NODE_ENV === "DEV") {
            this.configureDev();
        } else {
            this.configureServer();
        }
    }

    private configureDev() {
        configure({
            appenders: { 
                console: { 
                    type: 'console', 
                    layout: 
                    { 
                        type: 'pattern',
                        pattern: '%[[%d] [%p] %f{1}%] %m'
                    } 
                }
            },
            categories: { default: { appenders: ["console"], level: "debug" } }
        });
    }

    private configureServer() {    
        configure({
            appenders: { 
                gelf: { 
                    type: '@log4js-node/gelf', 
                    host: process.env.LOGSTASH_HOST, 
                    customFields: { '_app_name': process.env.PREFIX }
                }
            },
            categories: { default: { appenders: ["gelf"], level: "info" } }
        });
    }

    private getMeta() {        
        const params:any = this.request.params;        
        if(params)
            return {
                GELF: true, 
                "_transactionId": params.transactionId,
                "_agentSign": params.agentSign,
                "_branchId": params.branchId,
                "_username": params.username
            };
        else
            return '';
    }
}
