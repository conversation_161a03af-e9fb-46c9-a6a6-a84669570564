# Padrão de Merge Request (MR)

## Estrutura do Merge Request

Todo Merge Request deve seguir o padrão descrito abaixo, utilizando o template configurado no GitLab. Caso algum projeto ainda não tenha o template configurado, é **obrigatório** adicioná-lo antes de criar o MR.

### Exemplo de título do MR

O título de todos os Merge Requests deve conter o código da tarefa no Jira seguido de uma breve descrição da atividade. **Exemplo**:
**[JIRA-2343] - Corrigir cálculo de totais no checkout**

---

## Importância do Padrão

- **Clareza e Organização**: Seguir um padrão facilita o entendimento do que está sendo implementado ou corrigido.
- **Facilita Revisões**: Informações completas permitem que os revisores identifiquem erros ou melhorias de forma mais eficiente.
- **Release Notes**: As informações fornecidas no MR serão reutilizadas para gerar as notas de versão (release notes), garantindo que a equipe de produto e os stakeholders estejam alinhados sobre o que foi entregue.
- **Histórico Claro**: Manter um histórico claro e padronizado dos MRs ajuda na rastreabilidade de mudanças.

---

## Configurando o Template no GitLab

### Passo 1: Criar o Template

1. No repositório, acesse o diretório `.gitlab` (crie-o se não existir).
2. Dentro de `.gitlab`, crie uma pasta chamada `merge_request_templates` (se não existir).
3. Crie um arquivo chamado, por exemplo, `Default.md`.
4. Adicione o template padrão ao arquivo `Default.md`:

```md
# Jira task ID

[Task name](Cole a URL)

## Descrição

## Passos para reproduzir

(Como outra pessoa pode reproduzir essa atividade, É super importante)

## GIF ou Print

```

