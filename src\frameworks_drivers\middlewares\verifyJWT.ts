import { Request, Response } from 'express';
import jwtDecode from 'jwt-decode'
import jwt from 'jsonwebtoken';

export function verifyJWT(req: Request, res: Response, next: any) {
    const token: any = req.headers['api-token']
    if (!token) {
        return res.status(401)
            .json({ auth: false, message: 'No token provided.' });
    }    
    let decoded:any = jwtDecode(token);        
    let transactionId:any = req.headers['Gtw-Transaction-Id'] != undefined ? req.headers['Gtw-Transaction-Id'] : req.headers['gtw-transaction-id'];
    req.params.agentSign = decoded.credential.agentSign;
    req.params.branchId = decoded.credential.branchId;    
    req.params.transactionId = transactionId;
    next();
}

export function verifyJWTToken(req: Request, res: Response, next: any) {
    const token: any = req.headers['api-token']
    if (!token) {
        return res.status(401)
            .json({ auth: false, message: 'No token provided.' });
    }

    const secret: any = process.env.SECRET_JWT;
    jwt.verify(token, secret, (err: any, decoded: any) => {
        if (err)
            return res.status(500)
                .json({ auth: false, message: 'Failed to authenticate token.' });        
        req.params.jwt = decoded.data;
        next();
    });

}

