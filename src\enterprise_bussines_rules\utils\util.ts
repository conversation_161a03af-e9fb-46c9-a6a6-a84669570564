import moment from 'moment'
import ConsulConsult from '../../frameworks_drivers/middlewares/consulConsult'


export default class Util{

    public async convertZoneResponse(data:any){
        let response:any = []        
        data.forEach((zone:any) => {
            let zoneResponse:any = {};
            zoneResponse.id = zone.id;
            zoneResponse.name = zone.name;
            response.push(zoneResponse)
        });
        return response;
    }

    public async convertPropertyRequest(data:any){
        let request:any = {};
        request.dateFrom = moment(data.checkin, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss.sss") + "Z";
        request.dateTo =   moment(data.checkout, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss.sss") + "Z";
        //request.priceTo = 0;
        //request.priceFrom = 0;
        request.guest = data.pax.split(",").length;
        //request.bedRooms = 0;
        request.language = "pt"
        //request.currencyWished = "BRL";
        request.currencyWished = "PROPERTY_DEFAULT";                
        request.zones = [data.zone];
        request.amenities = (data.amenities != undefined && data.amenities != null) ? data.amenities : []        
        return request;
    }


    public async convertListPropertyResponse(data:any){
        let response:any = {categoryCount:{}, propertyList: []};
        let categoryCount = {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0};
        let numberOfBedrooms:any = [];
        data.forEach((property:any) => {            
            delete property.mcPropertyName;
            delete property.pricePerNightBase;
            property.zones[0] = property.zones[0].split(" ")[0];
            property.propertyName = property.propertyName.replace("___pt", "").trim();                        
            numberOfBedrooms.push(property.numberRooms);
            if(property.rateStars == 1){
                categoryCount['1'] = categoryCount['1'] + 1;
            }else if(property.rateStars == 2){
                categoryCount['2'] = categoryCount['2'] + 1;
            }else if(property.rateStars == 3){
                categoryCount['3'] = categoryCount['3'] + 1;
            }else if(property.rateStars == 4){
                categoryCount['4'] = categoryCount['4'] + 1;
            }else if(property.rateStars == 5){                
                categoryCount['5'] = categoryCount['5'] + 1;
            }
            response.propertyList.push(property)
        });
        numberOfBedrooms = [...new Set(numberOfBedrooms)];         
        let maxRooms = Math.max(...numberOfBedrooms);
        let minRooms = Math.min(...numberOfBedrooms);
        response.maxRooms = maxRooms;
        response.minRooms = minRooms;
        response.categoryCount = categoryCount;
        return response;
    }    

    public async convertPropertyDetailRequest(data:any){        
        let request:any = {};
        request.id = data.id;
        request.checkinDate = moment(data.checkin, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss.sss") + "Z";
        request.checkoutDate = moment(data.checkout, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss.sss") + "Z";
        //request.currencyWished = "BRL";
        request.currencyWished = "PROPERTY_DEFAULT";
        request.language = "pt";
        request.withCache = true;
        return request;
    }

    public async convertRateTokenToPropertyDetailRequest(data:any){        
        let request:any = {};
        request.id = data.idPmsExternal;
        request.checkinDate = moment(data.startDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss.sss") + "Z";
        request.checkoutDate = moment(data.endDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss.sss") + "Z";
        //request.currencyWished = "BRL";
        request.currencyWished = "PROPERTY_DEFAULT";
        request.language = "pt";
        request.withCache = true;
        return request;
    }

    public convertPropertyDetailResponse(data:any)
	{
        data.name = data.community.name;
        data.propertyName = data.propertyName.replace("___pt", "").trim();
		data.bathrooms = parseInt(data.bathrooms);
        delete data.propertyCategories;                
        delete data.housePictures3D;
        delete data.amenitiesNotIncluded;
        delete data.relatedProperties;           
        delete data.isAvailable;
        delete data.reservedDates;
        delete data.starsEvaluatedBy;
        delete data.status;
        delete data.statusDescription;
        delete data.priceBase;        
        return data;
    }

    public async convertAmenityResponse(data:any){
        let response:any = []        
        data.forEach((amenity:any) => {
            delete amenity.urlIcon;
            response.push(amenity)
        });
        return response;
    }

    
    public async convertDroolsRequest(request:any ,responseProperty:any, branchId:any, agentSign:any, endCountry:any){
        let houses:any = [];        
        responseProperty.propertyList.forEach((property:any) => {
            let house:any = {};            
            house.rph = 1;
            house.priceWithTax = property.price.total;
            house.priceWithoutTax = property.price.subtotal;
            house.rateToken = {}
            house.rateToken.agentSign = agentSign;
            house.rateToken.branchId = branchId;
            house.rateToken.endCountry = endCountry;
            house.rateToken.packageGroup = request.packageGroup.toUpperCase();
            house.rateToken.searchDate = moment(new Date()).format("YYYY-MM-DD");
            house.rateToken.startDate = moment(request.checkin, 'DD/MM/YYYY').format("YYYY-MM-DD");
            house.rateToken.endDate = moment(request.checkout, 'DD/MM/YYYY').format("YYYY-MM-DD");
            house.rateToken.paxs = request.pax;
            house.rateToken.playerPriceWithTax = property.price.total;
            house.rateToken.playerPriceWithoutTax = property.price.subtotal;
            house.playerCurrency = property.price.currencyDefault;
            house.currency = "BRL";
            house.rateToken.productType = "HOU";
            house.personPayment = property.country != "Brazil" ? 11762954 : 17892104;
            houses.push(house)
        });        
        return houses;
    }

    public async parsePropertyListRateToken(property:any, agentSign:any, branchId:any, request:any, jwt:any, types:any){


        let consulConsult = new ConsulConsult();         
        let notMarkupTax = await consulConsult.consultKey("notMarkupTax");
        notMarkupTax = (notMarkupTax === "true") ? true : false;

        let rateToken:any = {}

        rateToken.notMarkupTax = notMarkupTax;        
        rateToken.rph = 1;
        rateToken.keyRateToken = property.keyRateToken;
        rateToken.productId = property.id;
        rateToken.agentSign = agentSign;
        rateToken.branchId = branchId;
        rateToken.startState = jwt.startState;
        rateToken.startZoneId = jwt.startZoneId;
        rateToken.startCountry = jwt.startContry;
        rateToken.endState = jwt.endState;
        rateToken.endZoneId = jwt.endZoneId;
        rateToken.endCountry = jwt.endCountry;            
        rateToken.packageGroup = request.packageGroup.toUpperCase();
        rateToken.searchDate = moment(new Date()).format("YYYY-MM-DD");
        rateToken.startDate = moment(request.checkin, 'DD/MM/YYYY').format("YYYY-MM-DD");
        rateToken.endDate = moment(request.checkout, 'DD/MM/YYYY').format("YYYY-MM-DD");
        rateToken.paxs = request.pax;
        rateToken.price = property.price.subtotal;
        rateToken.priceWithTax = property.price.total;
        rateToken.priceWithoutTax = property.price.subtotal;
        rateToken.playerPriceWithTax = property.price.total;
        rateToken.playerPriceWithoutTax = property.price.subtotal;
        rateToken.playerCurrency = property.price.currencyDefault;
        rateToken.currency = "BRL";
        rateToken.productType = "HOU";
        rateToken.channelManagerId = 1;
        rateToken.playerId = 1;
        if(property.idProvider) rateToken.idProvider = property.idProvider;
        rateToken.exchangePcq = true;
        rateToken.type = types.filter((a:any)=>a.name==property.class)[0].id;
        rateToken.community = property.idCommunity;
        rateToken.personPayment = property.country != "Brazil" ? 11762954 : 17892104;
        if(property.price.total != property.price.subtotal){
            let taxFee = property.price.total - property.price.subtotal;
            let percent = (taxFee/property.price.subtotal) * 100
            //taxes = [{'description': 'tax_fee', 'amount':taxFee}];
            rateToken.taxes = "[Tax(code=E, description=tax_fee, percent=" + percent.toFixed(2) + ", amount=" + taxFee.toFixed(2) + ")]";            
        }
        return rateToken;
    }


    public isRefundable(checkin: string, daysToCancelWithoutExtraTaxes: number) {
        const start = new Date(moment(checkin, 'DD/MM/YYYY').format("YYYY-MM-DD"));
              start.setDate(start.getDate() - daysToCancelWithoutExtraTaxes);
        const now = new Date();
        const difference = start.getTime() - now.getTime();
        const daysOfPenalty = Math.ceil(difference / (1000 * 3600 * 24));
        const refundable = (daysOfPenalty >= 1);

        return refundable;
    }

    public getCVCZoneId(locationId: any, zoneIdCvcDictionary: any) {
        if (!locationId || !zoneIdCvcDictionary) return null;
        
        const id = Number(locationId);
        const list = JSON.parse(zoneIdCvcDictionary);
        const zone = list.find((data) => data.id === id);

        return zone ? zone.ZoneId : null;
    }

    public parsePropertyToRateToken(property:any, agentSign:any, branchId:any, request:any, jwt:any, prices: any, zoneIdCvcDictionary: any, methodName: string){
        const searchDate = moment(new Date()).format("YYYY-MM-DD");
        const checkin = moment(request.checkin, 'DD/MM/YYYY').format("YYYY-MM-DD");
        const checkout = moment(request.checkout, 'DD/MM/YYYY').format("YYYY-MM-DD");
        const packageGroup = request?.packageGroup?.toUpperCase()
        const refundable = this.isRefundable(request.checkin, property.daysToCancelWithoutExtraTaxes)
        const notMarkupTax = (prices?.rateToken?.playerCurrencyQuotation === "true") ? true : false;
        const zoneId = this.getCVCZoneId(request?.zone, zoneIdCvcDictionary);
        

        let params = `prd="HOU" cur="BRL" lan="pt_BR" pla="1" rph="1" `;
        
        if (methodName) params += `mtd="${methodName}" `
        if (searchDate) params += `sdt="${searchDate}" `;
        if (refundable) params += `nor="${refundable}" `;
        if (agentSign) params += `ags="${agentSign}" `;
        if (notMarkupTax) params += `aim="${notMarkupTax}" `;
        if (branchId) params += `bri="${branchId}" `;
        if (checkin) params += `dti="${checkin}" `;

        if (request.checkout) params += `dtf="${checkout}" `;
        if (packageGroup) params += `pkg="${packageGroup}" `;
        if (request.pax) params += `pxo="${request.pax}" pxs="${request.pax}" `;
        if (property.id) params += `pid="${property.id}" `;
        if (property.idProvider) params += `idp="${property.idProvider}" `;
        
        if (jwt.startCountry) params += `sct="${jwt.startContry}" `;
        if (jwt.startState) params += `sst="${jwt.startState}" `;
        if (jwt.startZoneId) params += `szi="${jwt.startZoneId}" `;
        if (jwt.endState) params += `est="${jwt.endState}" `;
        if (jwt.endCountry) params += `ect="${jwt.endCountry}" `;
        if (zoneId) params += `ezi="${zoneId}" `;

        if (prices.rateToken.markupId) params += `mki="${prices.rateToken.markupId}" `;
        if (prices.rateToken.markup) params += `mkp="${prices.rateToken.markup}" `;
        if (prices.rateToken.playerCurrencyQuotation) params += `pcq="${prices.rateToken.playerCurrencyQuotation}" `;
        if (prices.rateToken.playerCurrency) params += `plc="${prices.rateToken.playerCurrency}" `;
        if (prices.rateToken.playerPriceWithoutTax) params += `pot="${prices.rateToken.playerPriceWithoutTax}" `;
        if (prices.rateToken.personPayment) params += `ppy="${prices.rateToken.personPayment}" `;
        if (prices.rateToken.playerPriceWithTax) params += `pwt="${prices.rateToken.playerPriceWithTax}" `;
        if (prices.rateToken.sellPriceWithoutTax) params += `sot="${prices.rateToken.sellPriceWithoutTax}" `;
        if (prices.rateToken.sellPriceWithTax) params += `swt="${prices.rateToken.sellPriceWithTax}" `;
        if (prices.rateToken.channelManagerId) params += `cid="${prices.rateToken.channelManagerId}" `;

        /** 
         * xml exemple:
         *  <rateToken prd="HOU" cur="BRL" lan="pt_BR" mtd="HouseAvail" pla="1" rph="1" sdt="2022-12-28" nor="true" 
         *   ags="WEB" bri="1000" dti="2023-03-28" dtf="2023-04-02" pkg="STANDALONE" pxo="30,30" pxs="30,30" pid="493" 
         *   sst="SP" szi="9583" est="FL" ect="US" ezi="13601" mki="284510" mkp="0.64" pcq="5.4466" plc="USD" 
         *   pot="7200.4" ppy="11762954" pwt="8078.58" sot="61277.6541" swt="66060.749288" cid="1" />
         **/
        const xml = `<rateToken ${params}/>`;
        const buff = Buffer.from(xml);
        return buff.toString("base64");
    }

    public async parsePropertyDetailRateToken(property:any, agentSign:any, branchId:any, request:any, jwt:any, types:any){
        
        let consulConsult = new ConsulConsult();         
        let notMarkupTax = await consulConsult.consultKey("notMarkupTax");
        notMarkupTax = (notMarkupTax === "true") ? true : false;
        
        let rateToken:any = {}        
        rateToken.rph = 1;
        rateToken.notMarkupTax = notMarkupTax;
        rateToken.keyRateToken = property.keyRateToken;
        rateToken.productId = property.id;    
        rateToken.idPmsExternal = property.idPmsExternal;
        rateToken.agentSign = agentSign;
        rateToken.branchId = branchId;
        rateToken.startState = jwt.startState;
        rateToken.startZoneId = jwt.startZoneId;
        rateToken.startCountry = jwt.startContry;
        rateToken.endState = jwt.endState;
        rateToken.endZoneId = jwt.endZoneId;
        rateToken.endCountry = jwt.endCountry;            
        rateToken.packageGroup = request.packageGroup.toUpperCase();
        rateToken.searchDate = moment(new Date()).format("YYYY-MM-DD");
        rateToken.startDate = moment(request.checkin, 'DD/MM/YYYY').format("YYYY-MM-DD");
        rateToken.endDate = moment(request.checkout, 'DD/MM/YYYY').format("YYYY-MM-DD");
        rateToken.paxs = request.pax;
        rateToken.price = property.price.subtotal;
        rateToken.priceWithTax = property.price.total;
        rateToken.priceWithoutTax = property.price.subtotal;
        rateToken.playerPriceWithTax = property.price.total;
        rateToken.playerPriceWithoutTax = property.price.subtotal;
        rateToken.playerCurrency = property.price.currencyDefault;
        rateToken.currency = "BRL";
        rateToken.productType = "HOU";
        rateToken.channelManagerId = 1;
        rateToken.playerId = 1;
        rateToken.exchangePcq = true;
        if(property.idProvider) rateToken.idProvider = property.idProvider;
        rateToken.type = types.filter((a:any)=>a.name==property.propertyClass)[0].id;
        rateToken.community = property.community.id;
        rateToken.personPayment = property.country != "Brazil" ? 11762954 : 17892104;
        if(property.price.total != property.price.subtotal){
            let taxFee = property.price.total - property.price.subtotal;
            let percent = (taxFee/property.price.subtotal) * 100
            //taxes = [{'description': 'tax_fee', 'amount':taxFee}];
            rateToken.taxes = "[Tax(code=E, description=tax_fee, percent=" + percent.toFixed(2) + ", amount=" + taxFee.toFixed(2) + ")]";            
        }        
        return rateToken;
    }

        
	shallowedStringify( matter:any )
	{
		const done:any = {};
		const keys = Object.getOwnPropertyNames( matter );
		keys.forEach
		(
			function ( key )
			{
				done[ key ] = matter[ key ];
			}
		);
		return done;
	}

}
