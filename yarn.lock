# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz"
  integrity sha512-vG6SvB6oYEhvgisZNFRmRCUkLz11c7rp+tbNTynGqc6mS1d5ATd/sGyV6W0KZZnXRKMTzZDRgQT3Ou9jhpAfUg==
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/helper-validator-identifier@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.10.4.tgz"
  integrity sha512-3U9y+43hz7ZM+rzG24Qe2mufW5KhvFg/NhnNph+i9mgCtdTCtMJuI1TMkrIUiK7Ix4PYlRF9I5dhqaLYA/ADXw==

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.10.4.tgz"
  integrity sha512-i6rgnR/YgPEQzZZnbTHHuZdlE8qyoBNalD6F+q4vAFlcMEcqmkoG+mPqJYJCo63qPf74+Y1UZsl3l6f7/RIkmA==
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.9.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.10.4.tgz"
  integrity sha512-8jHII4hf+YVDsskTF6WuMB3X4Eh+PsUkC2ljq22so5rHvH+T8BzyL94VOdyFLNR8tBSVXOTbNHOKpR4TfRxVtA==

"@fast-csv/format@4.3.1":
  version "4.3.1"
  resolved "https://registry.npmjs.org/@fast-csv/format/-/format-4.3.1.tgz"
  integrity sha512-Ap6KSt0iJlzrivZU4grQzDGGOQ+vN5kvUeOHLF1BE7nWri1auiodgS3SCffvLe1Zvu79tACe1tw3dyBADk1NsA==
  dependencies:
    lodash.escaperegexp "^4.1.2"
    lodash.isboolean "^3.0.3"
    lodash.isequal "^4.5.0"
    lodash.isfunction "^3.0.9"
    lodash.isnil "^4.0.0"

"@fast-csv/parse@4.3.1":
  version "4.3.1"
  resolved "https://registry.npmjs.org/@fast-csv/parse/-/parse-4.3.1.tgz"
  integrity sha512-C73N77St4IHfVmT8Z2mWvLfvLyHWHyvGZVSG5mbhGefkmg9JztoaYpelf8ciqv99JzZ1ModpfuMWLwKEDTRL9A==
  dependencies:
    lodash.escaperegexp "^4.1.2"
    lodash.groupby "^4.6.0"
    lodash.isfunction "^3.0.9"
    lodash.isnil "^4.0.0"
    lodash.isundefined "^3.0.1"
    lodash.uniq "^4.5.0"

"@grpc/grpc-js@^1.0.5":
  version "1.1.5"
  resolved "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.1.5.tgz"
  integrity sha512-2huf5z85TdZI4nLmJQ9Zdfd+6vmIyBDs7B4L71bTaHKA9pRsGKAH24XaktMk/xneKJIqAgeIZtg1cyivVZtvrg==
  dependencies:
    "@grpc/proto-loader" "^0.6.0-pre14"
    "@types/node" "^12.12.47"
    google-auth-library "^6.0.0"
    semver "^6.2.0"

"@grpc/proto-loader@^0.5.4":
  version "0.5.5"
  resolved "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.5.5.tgz"
  integrity sha512-WwN9jVNdHRQoOBo9FDH7qU+mgfjPc8GygPYms3M+y3fbQLfnCe/Kv/E01t7JRgnrsOHH8euvSbed3mIalXhwqQ==
  dependencies:
    lodash.camelcase "^4.3.0"
    protobufjs "^6.8.6"

"@grpc/proto-loader@^0.6.0-pre14":
  version "0.6.0-pre9"
  resolved "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.6.0-pre9.tgz"
  integrity sha512-oM+LjpEjNzW5pNJjt4/hq1HYayNeQT+eGrOPABJnYHv7TyNPDNzkQ76rDYZF86X5swJOa4EujEMzQ9iiTdPgww==
  dependencies:
    "@types/long" "^4.0.1"
    lodash.camelcase "^4.3.0"
    long "^4.0.0"
    protobufjs "^6.9.0"
    yargs "^15.3.1"

"@kubernetes/client-node@0.10.2":
  version "0.10.2"
  resolved "https://registry.npmjs.org/@kubernetes/client-node/-/client-node-0.10.2.tgz"
  integrity sha512-JvsmxbTwiMqsh9LyuXMzT5HjoENFbB3a/JroJsobuAzkxN162UqAOvg++/AA+ccIMWRR2Qln4FyaOJ0a4eKyXg==
  dependencies:
    "@types/js-yaml" "^3.12.1"
    "@types/node" "^10.12.0"
    "@types/request" "^2.47.1"
    "@types/underscore" "^1.8.9"
    "@types/ws" "^6.0.1"
    isomorphic-ws "^4.0.1"
    js-yaml "^3.13.1"
    json-stream "^1.0.0"
    jsonpath-plus "^0.19.0"
    request "^2.88.0"
    shelljs "^0.8.2"
    tslib "^1.9.3"
    underscore "^1.9.1"
    ws "^6.1.0"

"@log4js-node/gelf@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@log4js-node/gelf/-/gelf-1.0.2.tgz"
  integrity sha512-/U3C8RUTmv2VoRLfvetG0fA+D8PBPRzr3vmwmljE7HXizxuk98zKIgaZy9U60vn888Ez6Ic2LG6+MDfa6UOLSw==
  dependencies:
    debug "^3.1.0"

"@nestcloud/boot@^0.4.4":
  version "0.4.4"
  resolved "https://registry.npmjs.org/@nestcloud/boot/-/boot-0.4.4.tgz"
  integrity sha512-HMpbx4vzJzS9yY58stClPaT7kE0bKQ8kOUpQowUNxfYCbgkD3Rjj8xmutebPv4ADdfpJVaQsTSbPenxUsiCXHw==
  dependencies:
    handlebars "^4.1.1"
    lodash "^4.17.11"
    reflect-metadata "^0.1.12"
    yamljs "^0.3.0"

"@nestcloud/boot@^0.7.5":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@nestcloud/boot/-/boot-0.7.15.tgz"
  integrity sha512-RjMFUfz6RwoMwSa52t6I9s6vrGjacBM7y1CkKgGk8jMg3zJQT0y5UFm1K7ZT2LkNs46LgSliRpKvwPErgLFg9g==
  dependencies:
    handlebars "^4.1.1"
    lodash "^4.17.11"
    reflect-metadata "^0.1.12"
    yamljs "^0.3.0"

"@nestcloud/common@^0.4.4", "@nestcloud/common@^0.7.5":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@nestcloud/common/-/common-0.7.15.tgz"
  integrity sha512-ta4ZRMXQ3jokzqOkfmGFisZkM7YjjcGX9uhsXW/m0Bz6Tq3+UgsTcDbJSKbgl9E7Uhb/r72vdhq5+WdKXBGoOQ==
  dependencies:
    kubernetes-client "^8.3.3"
    lodash "^4.17.11"
    yamljs "^0.3.0"

"@nestcloud/config@^0.7.5":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@nestcloud/config/-/config-0.7.15.tgz"
  integrity sha512-HY1m42TbBuTWWDoqeZ0fepHC3wzDs28p2Yw5dahgNdQPbdwAh42kz5ZhKpFfhA9uC8fP3QcfQGQLp8Zl2qtQKA==
  dependencies:
    etcd3 "^1.0.1"
    lodash "^4.17.11"
    reflect-metadata "^0.1.12"
    yamljs "^0.3.0"

"@nestcloud/consul-config@^0.4.4":
  version "0.4.4"
  resolved "https://registry.npmjs.org/@nestcloud/consul-config/-/consul-config-0.4.4.tgz"
  integrity sha512-8YS9QDODLrsE3EbHWI5/SZbueC7NbgT39LWow5/IuOsvPap4GqK3mfFT7aK8qiiKzPu8398Eq4kSsdNSqPJxmQ==
  dependencies:
    lodash "^4.17.11"
    reflect-metadata "^0.1.12"
    yamljs "^0.3.0"
  optionalDependencies:
    "@nestcloud/boot" "^0.4.4"

"@nestcloud/consul@^0.4.4", "@nestcloud/consul@^0.7.5":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@nestcloud/consul/-/consul-0.7.15.tgz"
  integrity sha512-psZWjGSUGFovpwSTEgCv6+fH+xxUlt989mDVdfdmyFzVwYggemsD8+SU5NTLAJOsfKBKsQDf8DcSOQSgrebl5w==
  dependencies:
    reflect-metadata "^0.1.13"
    yamljs "^0.3.0"

"@nestcloud/http@^0.7.1":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@nestcloud/http/-/http-0.7.15.tgz"
  integrity sha512-za8odQh9tV311buIpYHX49qLJctHCGlxi8jeWWQoOT51Jp9GHUU6715MQp+TneuWPZXsUIdy17WM7rffrhSAJA==
  dependencies:
    lodash "^4.17.11"
    reflect-metadata "^0.1.12"
    uri-params "^0.1.3"
    uuid "^3.3.2"

"@nestcloud/logger@^0.7.1":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@nestcloud/logger/-/logger-0.7.15.tgz"
  integrity sha512-6XuRN39OSIh6ygIv71SKESgyQQe09zFzt3JgctQLEf1poAKHAY2RuH/6eDhpzyrdJxLINDHNGG77dzorT/86SQ==
  dependencies:
    mkdirp "^0.5.1"
    moment "^2.24.0"
    reflect-metadata "^0.1.12"
    winston "^2.4.2"
    winston-daily-rotate-file "^3.8.0"

"@nestcloud/service@^0.7.1":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@nestcloud/service/-/service-0.7.15.tgz"
  integrity sha512-oWp4yO7NdtIhPWlR1gvJFOxSeZEXKKBsuoec44vfgp/f/A0KiU1Y4evxx/BPUSjglhcWqsq3s5KMcRlUfug3Cw==
  dependencies:
    blueimp-md5 "^2.10.0"
    lodash "^4.17.11"
    yamljs "^0.3.0"

"@nestjs/common@^6.0.0", "@nestjs/common@^6.7.0 || ^7.0.0", "@nestjs/common@^6.8.0 || ^7.0.0", "@nestjs/common@^7.0.0", "@nestjs/common@^7.0.8", "@nestjs/common@^7.3.2":
  version "7.4.2"
  resolved "https://registry.npmjs.org/@nestjs/common/-/common-7.4.2.tgz"
  integrity sha512-gHfoUPPufBDLUdkBdhC60rgMwyiOKIFVmxCnFNHsMH1mrbt1DcXQD+5nefZm0XaLY20QeBoqZjJDc4KdvE444w==
  dependencies:
    axios "0.19.2"
    cli-color "2.0.0"
    iterare "1.2.1"
    tslib "2.0.0"
    uuid "8.3.0"

"@nestjs/core@^6.7.0 || ^7.0.0", "@nestjs/core@^6.8.0 || ^7.0.0", "@nestjs/core@^7.0.0", "@nestjs/core@^7.3.2":
  version "7.4.2"
  resolved "https://registry.npmjs.org/@nestjs/core/-/core-7.4.2.tgz"
  integrity sha512-zLWVY+qj5RRIuDaL2J6a7f/Tn8wPMYOUHDZhXdRZFqonuY96dH9yc6DU9cWyX7fnmCIAxqdu5B7qd/S7VPHxow==
  dependencies:
    "@nuxtjs/opencollective" "0.2.2"
    fast-safe-stringify "2.0.7"
    iterare "1.2.1"
    object-hash "2.0.3"
    path-to-regexp "3.2.0"
    tslib "2.0.0"
    uuid "8.3.0"

"@nestjs/mapped-types@0.1.0":
  version "0.1.0"
  resolved "https://registry.npmjs.org/@nestjs/mapped-types/-/mapped-types-0.1.0.tgz"
  integrity sha512-FfQsZK5K1OvvGqjPHCJtrNTLlKLg7bLuphtCRTFb5K2P98JTfslauMbT7bS8huOoK/86HMNmNoHR/EVLAd4FzA==

"@nestjs/platform-express@^7.0.0":
  version "7.4.2"
  resolved "https://registry.npmjs.org/@nestjs/platform-express/-/platform-express-7.4.2.tgz"
  integrity sha512-pbwmUaKpAyLwp8iTfChvFqY/40wHXuqIPX2HGkHU0dbofd7sUpLJMlJCsOepR06/L+voRhmNC86yQm1EDAG7ag==
  dependencies:
    body-parser "1.19.0"
    cors "2.8.5"
    express "4.17.1"
    multer "1.4.2"
    tslib "2.0.0"

"@nestjs/swagger@^4.5.12":
  version "4.6.0"
  resolved "https://registry.npmjs.org/@nestjs/swagger/-/swagger-4.6.0.tgz"
  integrity sha512-B7DBMm+MTBfttGvtdb9quEbP9lgi5w3OFs30v/if0ZHE0Fl1B+t1FP8rnmR0xk5wfp7Wd/9LVl9ll/hvIheJ4w==
  dependencies:
    "@nestjs/mapped-types" "0.1.0"
    lodash "4.17.20"
    path-to-regexp "3.2.0"

"@nestjs/terminus@^7.0.1":
  version "7.0.1"
  resolved "https://registry.npmjs.org/@nestjs/terminus/-/terminus-7.0.1.tgz"
  integrity sha512-OKg1QQDb+whHJM3Xt+3RRUPiyZSyD0qLacfldK0TXcFpKyexA0yyY3GKeaBNApf01FEzJgkK3ARCUoELnAfXDA==
  dependencies:
    check-disk-space "2.1.0"
    deprecate "^1.1.1"

"@nestjs/typeorm@^7.1.0":
  version "7.1.1"
  resolved "https://registry.npmjs.org/@nestjs/typeorm/-/typeorm-7.1.1.tgz"
  integrity sha512-M6trL1AHlcYRPSk4/8l7pxFBZhMFBrgklMMK7irUP0NLe29CP2U7QC2P3C0yvbXiuPXUwpJo52P8otrcvX/8qg==
  dependencies:
    uuid "8.3.0"

"@nuxtjs/opencollective@0.2.2":
  version "0.2.2"
  resolved "https://registry.npmjs.org/@nuxtjs/opencollective/-/opencollective-0.2.2.tgz"
  integrity sha512-69gFVDs7mJfNjv9Zs5DFVD+pvBW+k1TaHSOqUWqAyTTfLcKI/EMYQgvEvziRd+zAFtUOoye6MfWh0qvinGISPw==
  dependencies:
    chalk "^2.4.1"
    consola "^2.3.0"
    node-fetch "^2.3.0"

"@panva/asn1.js@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/@panva/asn1.js/-/asn1.js-1.0.0.tgz"
  integrity sha512-UdkG3mLEqXgnlKsWanWcgb6dOjUzJ+XC5f+aWw30qrtjxeNUSfKX1cd5FBzOaXQumoe9nIqeZUvrRJS03HCCtw==

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  integrity sha1-m4sMxmPWaafY9vXQiToU00jzD78=

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz"
  integrity sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  integrity sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  integrity sha1-NVy8mLr61ZePntCV85diHx0Ga3A=

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  integrity sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz"
  integrity sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  integrity sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz"
  integrity sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz"
  integrity sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  integrity sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=

"@sindresorhus/is@^0.14.0":
  version "0.14.0"
  resolved "https://registry.npmjs.org/@sindresorhus/is/-/is-0.14.0.tgz"
  integrity sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ==

"@szmarczak/http-timer@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.1.2.tgz"
  integrity sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==
  dependencies:
    defer-to-connect "^1.0.1"

"@types/aes-js@^3.1.1":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@types/aes-js/-/aes-js-3.1.1.tgz"
  integrity sha512-SDSGgXT3LRCH6qMWk8OHT1vLSVNuHNvCpKCx2/TYtQMbMGGgxJC9fspwSkQjqzRagrWnCrxuLL3jMNXLXHHvSw==

"@types/async-redis@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@types/async-redis/-/async-redis-1.1.1.tgz"
  integrity sha512-NUS/27r3XP+3hXAzsV9dGnk6JXXIQErP5xHHlGVqnC+V0gBqAzQy9MojjcDWmhR9nhZ8xp3p6GvR4X3Yl5U7Kw==
  dependencies:
    "@types/redis" "*"

"@types/atob@^2.1.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@types/atob/-/atob-2.1.2.tgz"
  integrity sha512-8GAYQ1jDRUQkSpHzJUqXwAkYFOxuWAOGLhIR4aPd/Y/yL12Q/9m7LsKpHKlfKdNE/362Hc9wPI1Yh6opDfxVJg==

"@types/aws-sdk@^2.7.0":
  version "2.7.0"
  resolved "https://registry.npmjs.org/@types/aws-sdk/-/aws-sdk-2.7.0.tgz"
  integrity sha1-g1iLPRTr3KHUzl4CM4dXdWjOgvM=
  dependencies:
    aws-sdk "*"

"@types/axios@^0.14.0":
  version "0.14.0"
  resolved "https://registry.npmjs.org/@types/axios/-/axios-0.14.0.tgz"
  integrity sha1-7CMA++fX3d1+udOr+HmZlkyvzkY=
  dependencies:
    axios "*"

"@types/bluebird@^3.5.32":
  version "3.5.32"
  resolved "https://registry.npmjs.org/@types/bluebird/-/bluebird-3.5.32.tgz"
  integrity sha512-dIOxFfI0C+jz89g6lQ+TqhGgPQ0MxSnh/E4xuC0blhFtyW269+mPG5QeLgbdwst/LvdP8o1y0o/Gz5EHXLec/g==

"@types/body-parser@*":
  version "1.19.0"
  resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.0.tgz"
  integrity sha512-W98JrE0j2K78swW4ukqMleo8R7h/pFETjM2DQ90MF6XK2i4LO4W3gQ71Lt4w3bfm2EvVSyWHplECvB5sK22yFQ==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/btoa@^1.2.3":
  version "1.2.3"
  resolved "https://registry.npmjs.org/@types/btoa/-/btoa-1.2.3.tgz"
  integrity sha512-ANNCZICS/ofxhzUl8V1DniBJs+sFQ+Yg5am1ZwVEf/sxoKY/J2+h5Fuw3xUErlZ7eJLdgzukBjZwnsV6+/2Rmg==
  dependencies:
    "@types/node" "*"

"@types/bunyan@^1.8.8":
  version "1.8.8"
  resolved "https://registry.npmjs.org/@types/bunyan/-/bunyan-1.8.8.tgz"
  integrity sha512-Cblq+Yydg3u+sGiz2mjHjC5MPmdjY+No4qvHrF+BUhblsmSfMvsHLbOG62tPbonsqBj6sbWv1LHcsoe5Jw+/Ow==
  dependencies:
    "@types/node" "*"

"@types/caseless@*":
  version "0.12.2"
  resolved "https://registry.npmjs.org/@types/caseless/-/caseless-0.12.2.tgz"
  integrity sha512-6ckxMjBBD8URvjB6J3NcnuAn5Pkl7t3TizAg+xdlzzQGSPSmBcXf8KoIH0ua/i+tio+ZRUHEXp0HEmvaR4kt0w==

"@types/color-name@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@types/color-name/-/color-name-1.1.1.tgz"
  integrity sha512-rr+OQyAjxze7GgWrSaJwydHStIhHq2lvY3BOC2Mj7KnzI7XK0Uw1TOOdI9lDoajEbSWLiYgoo4f1R51erQfhPQ==

"@types/compression@^1.0.1":
  version "1.7.0"
  resolved "https://registry.npmjs.org/@types/compression/-/compression-1.7.0.tgz"
  integrity sha512-3LzWUM+3k3XdWOUk/RO+uSjv7YWOatYq2QADJntK1pjkk4DfVP0KrIEPDnXRJxAAGKe0VpIPRmlINLDuCedZWw==
  dependencies:
    "@types/express" "*"

"@types/connect@*":
  version "3.4.33"
  resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.33.tgz"
  integrity sha512-2+FrkXY4zllzTNfJth7jOqEHC+enpLeGslEhpnTAkg21GkRrWV4SsAtqchtT4YS9/nODBU2/ZfsBY2X4J/dX7A==
  dependencies:
    "@types/node" "*"

"@types/consul@^0.23.34":
  version "0.23.34"
  resolved "https://registry.npmjs.org/@types/consul/-/consul-0.23.34.tgz"
  integrity sha512-8h+W+TPuqhXQD/dYj3hThy2qQAFRGSe5NKTJ3c9Xd8/OceNEyYG1ys+BAT2OlEWRpsvqbL6H9LRhoHij3vECIg==
  dependencies:
    "@types/node" "*"

"@types/cors@^2.8.6":
  version "2.8.6"
  resolved "https://registry.npmjs.org/@types/cors/-/cors-2.8.6.tgz"
  integrity sha512-invOmosX0DqbpA+cE2yoHGUlF/blyf7nB0OGYBBiH27crcVm5NmFaZkLP4Ta1hGaesckCi5lVLlydNJCxkTOSg==
  dependencies:
    "@types/express" "*"

"@types/crypto-js@^3.1.47":
  version "3.1.47"
  resolved "https://registry.npmjs.org/@types/crypto-js/-/crypto-js-3.1.47.tgz"
  integrity sha512-eI6gvpcGHLk3dAuHYnRCAjX+41gMv1nz/VP55wAe5HtmAKDOoPSfr3f6vkMc08ov1S0NsjvUBxDtHHxqQY1LGA==

"@types/cryptr@^4.0.1":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@types/cryptr/-/cryptr-4.0.1.tgz"
  integrity sha512-Nn8fvr+8XYWK5h422lj4xQACfOg6vdhKI+Rh9ERa5Mg0cZvPL9Vn3845vSY07dNZnEs5cqkSNVMdhmf70lAYkA==

"@types/express-serve-static-core@*":
  version "4.17.8"
  resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.8.tgz"
  integrity sha512-1SJZ+R3Q/7mLkOD9ewCBDYD2k0WyZQtWYqF/2VvoNN2/uhI49J9CDN4OAm+wGMA0DbArA4ef27xl4+JwMtGggw==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*", "@types/express@^4.17.2":
  version "4.17.7"
  resolved "https://registry.npmjs.org/@types/express/-/express-4.17.7.tgz"
  integrity sha512-dCOT5lcmV/uC2J9k0rPafATeeyz+99xTt54ReX11/LObZgfzJqZNcW27zGhYyX+9iSEGXGt5qLPwRSvBZcLvtQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "*"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/got@^9.6.9":
  version "9.6.11"
  resolved "https://registry.npmjs.org/@types/got/-/got-9.6.11.tgz"
  integrity sha512-dr3IiDNg5TDesGyuwTrN77E1Cd7DCdmCFtEfSGqr83jMMtcwhf/SGPbN2goY4JUWQfvxwY56+e5tjfi+oXeSdA==
  dependencies:
    "@types/node" "*"
    "@types/tough-cookie" "*"
    form-data "^2.5.0"

"@types/helmet@^0.0.45":
  version "0.0.45"
  resolved "https://registry.npmjs.org/@types/helmet/-/helmet-0.0.45.tgz"
  integrity sha512-PsLZI1NqKpXvsMZxh66xAZtpKiTeW+swY8a8LnCNSBbM/mvwU41P3BYoEqkJM9RbITPsq4uhIH0NkIsL9fzPbg==
  dependencies:
    "@types/express" "*"

"@types/js-yaml@^3.12.1":
  version "3.12.5"
  resolved "https://registry.npmjs.org/@types/js-yaml/-/js-yaml-3.12.5.tgz"
  integrity sha512-JCcp6J0GV66Y4ZMDAQCXot4xprYB+Zfd3meK9+INSJeVZwJmHAW30BBEEkPzXswMXuiyReUGOP3GxrADc9wPww==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/jsonwebtoken@^8.3.7":
  version "8.5.0"
  resolved "https://registry.npmjs.org/@types/jsonwebtoken/-/jsonwebtoken-8.5.0.tgz"
  integrity sha512-9bVao7LvyorRGZCw0VmH/dr7Og+NdjYSsKAxB43OQoComFbBgsEpoR9JW6+qSq/ogwVBg8GI2MfAlk4SYI4OLg==
  dependencies:
    "@types/node" "*"

"@types/jwt-decode@^2.2.1":
  version "2.2.1"
  resolved "https://registry.npmjs.org/@types/jwt-decode/-/jwt-decode-2.2.1.tgz"
  integrity sha512-aWw2YTtAdT7CskFyxEX2K21/zSDStuf/ikI3yBqmwpwJF0pS+/IX5DWv+1UFffZIbruP6cnT9/LAJV1gFwAT1A==

"@types/long@^4.0.1":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@types/long/-/long-4.0.1.tgz"
  integrity sha512-5tXH6Bx/kNGd3MgffdmP4dy2Z+G4eaXw0SE81Tq3BNadtnMR5/ySMzX4SLEzHJzSmPNn4HIdpQsBvXMUykr58w==

"@types/mime@*":
  version "2.0.2"
  resolved "https://registry.npmjs.org/@types/mime/-/mime-2.0.2.tgz"
  integrity sha512-4kPlzbljFcsttWEq6aBW0OZe6BDajAmyvr2xknBG92tejQnvdGtT9+kXSZ580DqpxY9qG2xeQVF9Dq0ymUTo5Q==

"@types/moment@^2.13.0":
  version "2.13.0"
  resolved "https://registry.npmjs.org/@types/moment/-/moment-2.13.0.tgz"
  integrity sha1-YE69GJvDvDShVIaJQE5hoqSqyJY=
  dependencies:
    moment "*"

"@types/node@*", "@types/node@^13.5.0", "@types/node@^13.7.0":
  version "13.13.13"
  resolved "https://registry.npmjs.org/@types/node/-/node-13.13.13.tgz"
  integrity sha512-UfvBE9oRCAJVzfR+3eWm/sdLFe/qroAPEXP3GPJ1SehQiEVgZT6NQZWYbPMiJ3UdcKM06v4j+S1lTcdWCmw+3g==

"@types/node@^10.12.0":
  version "10.17.28"
  resolved "https://registry.npmjs.org/@types/node/-/node-10.17.28.tgz"
  integrity sha512-dzjES1Egb4c1a89C7lKwQh8pwjYmlOAG9dW1pBgxEk57tMrLnssOfEthz8kdkNaBd7lIqQx7APm5+mZ619IiCQ==

"@types/node@^12.12.47":
  version "12.12.54"
  resolved "https://registry.npmjs.org/@types/node/-/node-12.12.54.tgz"
  integrity sha512-ge4xZ3vSBornVYlDnk7yZ0gK6ChHf/CHB7Gl1I0Jhah8DDnEQqBzgohYG4FX4p81TNirSETOiSyn+y1r9/IR6w==

"@types/node@^14.0.1":
  version "14.0.19"
  resolved "https://registry.npmjs.org/@types/node/-/node-14.0.19.tgz"
  integrity sha512-yf3BP/NIXF37BjrK5klu//asUWitOEoUP5xE1mhSUjazotwJ/eJDgEmMQNlOeWOVv72j24QQ+3bqXHE++CFGag==

"@types/oracledb@^4.2.3":
  version "4.2.3"
  resolved "https://registry.npmjs.org/@types/oracledb/-/oracledb-4.2.3.tgz"
  integrity sha512-NGR1quTuW97cxucPG9vratZjWVOtE960+hfKm5N616hmSiB1Ls9UpbBWROr0FfH/yUhHZZJ9ru1IHcmLLXwVEw==
  dependencies:
    "@types/node" "*"
    dotenv "^8.2.0"

"@types/qs@*":
  version "6.9.3"
  resolved "https://registry.npmjs.org/@types/qs/-/qs-6.9.3.tgz"
  integrity sha512-7s9EQWupR1fTc2pSMtXRQ9w9gLOcrJn+h7HOXw4evxyvVqMi4f+q7d2tnFe3ng3SNHjtK+0EzGMGFUQX4/AQRA==

"@types/range-parser@*":
  version "1.2.3"
  resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.3.tgz"
  integrity sha512-ewFXqrQHlFsgc09MK5jP5iR7vumV/BYayNC6PgJO2LPe8vrnNFyjQjSppfEngITi0qvfKtzFvgKymGheFM9UOA==

"@types/redis@*", "@types/redis@^2.8.32":
  version "2.8.32"
  resolved "https://registry.npmjs.org/@types/redis/-/redis-2.8.32.tgz"
  integrity sha512-7jkMKxcGq9p242exlbsVzuJb57KqHRhNl4dHoQu2Y5v9bCAbtIXXH0R3HleSQW4CTOqpHIYUW3t6tpUj4BVQ+w==
  dependencies:
    "@types/node" "*"

"@types/request@^2.47.1":
  version "2.48.5"
  resolved "https://registry.npmjs.org/@types/request/-/request-2.48.5.tgz"
  integrity sha512-/LO7xRVnL3DxJ1WkPGDQrp4VTV1reX9RkC85mJ+Qzykj2Bdw+mG15aAfDahc76HtknjzE16SX/Yddn6MxVbmGQ==
  dependencies:
    "@types/caseless" "*"
    "@types/node" "*"
    "@types/tough-cookie" "*"
    form-data "^2.5.0"

"@types/serve-static@*":
  version "1.13.4"
  resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.4.tgz"
  integrity sha512-jTDt0o/YbpNwZbQmE/+2e+lfjJEJJR0I3OFaKQKPWkASkCoW3i6fsUnqudSMcNAfbtmADGu8f4MV4q+GqULmug==
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/mime" "*"

"@types/swagger-ui-express@^4.1.1":
  version "4.1.2"
  resolved "https://registry.npmjs.org/@types/swagger-ui-express/-/swagger-ui-express-4.1.2.tgz"
  integrity sha512-t9teFTU8dKe69rX9EwL6OM2hbVquYdFM+sQ0REny4RalPlxAm+zyP04B12j4c7qEuDS6CnlwICywqWStPA3v4g==
  dependencies:
    "@types/express" "*"
    "@types/serve-static" "*"

"@types/tough-cookie@*":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.0.tgz"
  integrity sha512-I99sngh224D0M7XgW1s120zxCt3VYQ3IQsuw3P3jbq5GG4yc79+ZjyKznyOGIQrflfylLgcfekeZW/vk0yng6A==

"@types/underscore@^1.8.9":
  version "1.10.22"
  resolved "https://registry.npmjs.org/@types/underscore/-/underscore-1.10.22.tgz"
  integrity sha512-fiJulOOmc747q+mZwBtLyBu6yBX2uI4biuQ1Y3JvcU7YjmdOEOracUXTiET/PAWI2hhoUH1t4HbwJj42YEnbkg==

"@types/uuid@^3.4.7":
  version "3.4.9"
  resolved "https://registry.npmjs.org/@types/uuid/-/uuid-3.4.9.tgz"
  integrity sha512-XDwyIlt/47l2kWLTzw/mtrpLdB+GPSskR2n/PIcPn+VYhVO77rGhRncIR5GPU0KRzXuqkDO+J5qqrG0Y8P6jzQ==

"@types/ws@^6.0.1":
  version "6.0.4"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-6.0.4.tgz"
  integrity sha512-PpPrX7SZW9re6+Ha8ojZG4Se8AZXgf0GK6zmfqEuCsY49LFDNXO3SByp44X3dFEqtB73lkCDAdUazhAjVPiNwg==
  dependencies:
    "@types/node" "*"

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz"
  integrity sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-jsx@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.2.0.tgz"
  integrity sha512-HiUX/+K2YpkpJ+SzBffkM/AQ2YE03S0U1kjTLVpoJdhZMOWy8qvXVN9JdLqv2QsaQ6MPYQIuNmwD8zOiYUofLQ==

"acorn@^6.0.0 || ^7.0.0", acorn@^7.1.1:
  version "7.3.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-7.3.1.tgz"
  integrity sha512-tLc0wSnatxAQHVHUapaHdz72pi9KUyHjq5KyHjGg9Y8Ifdc79pTh2XvI6I1/chZbnM7QtNKzh66ooDogPZSleA==

adler-32@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/adler-32/-/adler-32-1.2.0.tgz"
  integrity sha1-aj5r8KY5ALoVZSgIyxXGgT0aXyU=
  dependencies:
    exit-on-epipe "~1.0.1"
    printj "~1.1.0"

aes-es@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/aes-es/-/aes-es-3.0.0.tgz"
  integrity sha512-umoGeZk6xwADiHq5FPXdgLQ8mhmWIphJAGMv9oxMtAC+P0Z1MJYESzN48lbQEP9GzN92EMyJFcGrqSefx9kL2w==

aes-js@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/aes-js/-/aes-js-3.1.2.tgz"
  integrity sha512-e5pEa2kBnBOgR4Y/p20pskXI74UEz7de8ZGVo58asOtvSVG5YAbJeELPZxOmt+Bnz3rX753YKhfIn4X4l1PPRQ==

agent-base@6:
  version "6.0.1"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.1.tgz"
  integrity sha512-01q25QQDwLSsyfhrKbn8yuur+JNw0H+0Y4JiGIKd3z9aYk/w/2kxD/Upc+t2ZBBSUNff50VjPsSW2YxM8QYKVg==
  dependencies:
    debug "4"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.3:
  version "6.12.3"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.3.tgz"
  integrity sha512-4K0cK3L1hsqk9xIb2z9vs/XU+PGJZ9PNpJRDS9YLzmNdX6jmVPfamLvTJr0aDAusnHyCHO6MjzlkAsgtqp9teA==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-align@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/ansi-align/-/ansi-align-3.0.0.tgz"
  integrity sha512-ZpClVKqXN3RGBmKibdfWzqCY4lnjEuoNzU5T0oEFpfd/z5qJHVarukridD4juLO2FXMiwUQxr9WqQtaYa8XRYw==
  dependencies:
    string-width "^3.0.0"

ansi-escapes@^4.2.1:
  version "4.3.1"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.1.tgz"
  integrity sha512-JWF7ocqNrp8u9oqpgV+wH5ftbt+cfvv+PTjOvKLT3AdYly/LmORARfEVT1iyjwN+4MqE5UmVKoAdIBqeoCHgLA==
  dependencies:
    type-fest "^0.11.0"

ansi-regex@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.0.tgz"
  integrity sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg==

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.0.tgz"
  integrity sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg==

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.2.1.tgz"
  integrity sha512-9VGjrMsG1vePxcSweQsN20KY/c4zN0h9fLjqAbwbPfahM3t+NL+M9HC8xeXG2I8pX5NoamTGNuomEUFI7fcUjA==
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

ansi-styles@^4.1.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.2.1.tgz"
  integrity sha512-9VGjrMsG1vePxcSweQsN20KY/c4zN0h9fLjqAbwbPfahM3t+NL+M9HC8xeXG2I8pX5NoamTGNuomEUFI7fcUjA==
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

anymatch@~3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.1.tgz"
  integrity sha512-mM8522psRCqzV+6LhomX5wgp25YVibjh8Wj23I5RPkPppSVSjyKD2A2mBJmWGa+KN7f2D6LNh9jkBCeyLktzjg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

append-field@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz"
  integrity sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=

archiver-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/archiver-utils/-/archiver-utils-2.1.0.tgz"
  integrity sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==
  dependencies:
    glob "^7.1.4"
    graceful-fs "^4.2.0"
    lazystream "^1.0.0"
    lodash.defaults "^4.2.0"
    lodash.difference "^4.5.0"
    lodash.flatten "^4.4.0"
    lodash.isplainobject "^4.0.6"
    lodash.union "^4.6.0"
    normalize-path "^3.0.0"
    readable-stream "^2.0.0"

archiver@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/archiver/-/archiver-4.0.1.tgz"
  integrity sha512-/YV1pU4Nhpf/rJArM23W6GTUjT0l++VbjykrCRua1TSXrn+yM8Qs7XvtwSiRse0iCe49EPNf7ktXnPsWuSb91Q==
  dependencies:
    archiver-utils "^2.1.0"
    async "^2.6.3"
    buffer-crc32 "^0.2.1"
    glob "^7.1.6"
    readable-stream "^3.6.0"
    tar-stream "^2.1.2"
    zip-stream "^3.0.1"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  integrity sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-includes@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.1.tgz"
  integrity sha512-c2VXaCHl7zPsvpkFsw4nxvFie4fh1ur9bpcgsVkIjqn0H/Xwdg+7fv3n2r/isyS8EBj5b06M9kHyZuIr4El6WQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0"
    is-string "^1.0.5"

array.prototype.flat@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.2.3.tgz"
  integrity sha512-gBlRZV0VSmfPIeWfuuy56XZMvbVfbEUnOXUvt3F/eUUUSyzlgLxhEX4YAEpxNAogRGehPSnfXyPtYyKAhkzQhQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

arrify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/arrify/-/arrify-2.0.1.tgz"
  integrity sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==

asn1@~0.2.3:
  version "0.2.4"
  resolved "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz"
  integrity sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg==
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@^1.0.0, assert-plus@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/astral-regex/-/astral-regex-1.0.0.tgz"
  integrity sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz"
  integrity sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==

async-redis@^1.1.7:
  version "1.1.7"
  resolved "https://registry.npmjs.org/async-redis/-/async-redis-1.1.7.tgz"
  integrity sha512-phpZe2/U+Ih4Lpy72KWF4+c8gymsUgzg6NV/TZUb8BLNn7soQewFxqcq9nndobfPmzXiuhMLi6GNBiQVIor/EA==
  dependencies:
    redis "^2.8.0"
    redis-commands "^1.3.1"

async@^2.5.0, async@^2.6.3:
  version "2.6.3"
  resolved "https://registry.npmjs.org/async/-/async-2.6.3.tgz"
  integrity sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==
  dependencies:
    lodash "^4.17.14"

async@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/async/-/async-1.0.0.tgz"
  integrity sha1-+PwEyjoTeErenhZBr5hXjPvWR6k=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  integrity sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==

aws-sdk@*, aws-sdk@^2.610.0:
  version "2.711.0"
  resolved "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.711.0.tgz"
  integrity sha512-u2dt1k7vBE5sIzzMNRB+xCSHJ8vNmqeF8/KALdzoFtbBSxM6zKl8My3aV24rNiEC2lEA0PWTQEe9130hulOr2Q==
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.15.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    uuid "3.3.2"
    xml2js "0.4.19"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.10.1"
  resolved "https://registry.npmjs.org/aws4/-/aws4-1.10.1.tgz"
  integrity sha512-zg7Hz2k5lI8kb7U32998pRRFin7zJlkfezGJjUc2heaD4Pw2wObakCDVzkKztTm/Ln7eiVvYsjqak0Ed4LkMDA==

axios@*, axios@^0.19.2, axios@0.19.2:
  version "0.19.2"
  resolved "https://registry.npmjs.org/axios/-/axios-0.19.2.tgz"
  integrity sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==
  dependencies:
    follow-redirects "1.5.10"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base64-js@^1.0.2, base64-js@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.3.1.tgz"
  integrity sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g==

base64url@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/base64url/-/base64url-3.0.1.tgz"
  integrity sha512-ir1UPr3dkwexU7FdV8qBBbNDRUhMmIekYMFZfi+C/sLNnRESKPl23nB9b2pltqfOQNnGzsDdId90AEtG5tCx4A==

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

big-integer@^1.6.17:
  version "1.6.48"
  resolved "https://registry.npmjs.org/big-integer/-/big-integer-1.6.48.tgz"
  integrity sha512-j51egjPa7/i+RdiRuJbPdJ2FIUYYPhvYLjzoYbcMMm62ooO6F94fETG4MTs46zPAF9Brs04OajboA/qTGuz78w==

bignumber.js@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.0.0.tgz"
  integrity sha512-t/OYhhJ2SD+YGBQcjY8GzzDHEk9f3nerxjtfa6tlMXfe7frs/WozhvCNoGvpM0P3bNf3Gq5ZRMlGr5f3r4/N8A==

binary-extensions@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.1.0.tgz"
  integrity sha512-1Yj8h9Q+QDF5FzhMs/c9+6UntbD5MkRfRwac8DoEm9ZfUBZ7tZ55YcGVAzEe4bXsdQHEk+s9S5wsOKVdZrw0tQ==

binary@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/binary/-/binary-0.3.0.tgz"
  integrity sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk=
  dependencies:
    buffers "~0.1.1"
    chainsaw "~0.1.0"

bl@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/bl/-/bl-4.0.2.tgz"
  integrity sha512-j4OH8f6Qg2bGuWfRiltT2HYGx0e1QcBTrK9KAHNMwMZdQnDZFk0ZSYIpADjYCB3U12nicC5tVJwSIhwOWjb4RQ==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bluebird@^3.7.2:
  version "3.7.2"
  resolved "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==

bluebird@~3.4.1:
  version "3.4.7"
  resolved "https://registry.npmjs.org/bluebird/-/bluebird-3.4.7.tgz"
  integrity sha1-9y12C+Cbf3bQjtj66Ysomo0F+rM=

blueimp-md5@^2.10.0:
  version "2.17.0"
  resolved "https://registry.npmjs.org/blueimp-md5/-/blueimp-md5-2.17.0.tgz"
  integrity sha512-x5PKJHY5rHQYaADj6NwPUR2QRCUVSggPzrUKkeENpj871o9l9IefJbO2jkT5UvYykeOK9dx0VmkIo6dZ+vThYw==

body-parser@1.19.0:
  version "1.19.0"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.19.0.tgz"
  integrity sha512-dhEPs72UPbDnAQJ9ZKMNTP6ptJaionhP5cBb541nXPlW60Jepo9RV/a4fX4XWW9CuFNK22krhrj1+rgzifNCsw==
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bowser@2.9.0:
  version "2.9.0"
  resolved "https://registry.npmjs.org/bowser/-/bowser-2.9.0.tgz"
  integrity sha512-2ld76tuLBNFekRgmJfT2+3j5MIrP6bFict8WAIT3beq+srz1gcKNAdNKMqHqauQt63NmAa88HfP1/Ypa9Er3HA==

boxen@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/boxen/-/boxen-4.2.0.tgz"
  integrity sha512-eB4uT9RGzg2odpER62bBwSLvUeGC+WbRjjyyFhGsKnc8wp/m0+hQsMUvUe3H2V0D5vw0nBdO1hCJoZo5mKeuIQ==
  dependencies:
    ansi-align "^3.0.0"
    camelcase "^5.3.1"
    chalk "^3.0.0"
    cli-boxes "^2.2.0"
    string-width "^4.1.0"
    term-size "^2.1.0"
    type-fest "^0.8.1"
    widest-line "^3.1.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

btoa@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz"
  integrity sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==

buffer-crc32@^0.2.1, buffer-crc32@^0.2.13:
  version "0.2.13"
  resolved "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz"
  integrity sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==

buffer-indexof-polyfill@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/buffer-indexof-polyfill/-/buffer-indexof-polyfill-1.0.1.tgz"
  integrity sha1-qfuAbOgUXVQoUQznLyeLs2OmOL8=

buffer@^5.1.0:
  version "5.6.0"
  resolved "https://registry.npmjs.org/buffer/-/buffer-5.6.0.tgz"
  integrity sha512-/gDYp/UtU0eA1ys8bOs9J6a+E/KWIY+DZ+Q2WESNUA0jFRsJOc0SNUO6xJ5SGA1xueg3NL65W6s+NY5l9cunuw==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"

buffer@^5.5.0:
  version "5.6.0"
  resolved "https://registry.npmjs.org/buffer/-/buffer-5.6.0.tgz"
  integrity sha512-/gDYp/UtU0eA1ys8bOs9J6a+E/KWIY+DZ+Q2WESNUA0jFRsJOc0SNUO6xJ5SGA1xueg3NL65W6s+NY5l9cunuw==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"

buffer@4.9.2:
  version "4.9.2"
  resolved "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffers@~0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/buffers/-/buffers-0.1.1.tgz"
  integrity sha1-skV5w77U1tOWru5tmorn9Ugqt7s=

bunyan@^1.8.15:
  version "1.8.15"
  resolved "https://registry.npmjs.org/bunyan/-/bunyan-1.8.15.tgz"
  integrity sha512-0tECWShh6wUysgucJcBAoYegf3JJoZWibxdqhTm7OHPeT42qdjkZ29QCMcKwbgU1kiH+auSIasNRXMLWXafXig==
  optionalDependencies:
    dtrace-provider "~0.8"
    moment "^2.19.3"
    mv "~2"
    safe-json-stringify "~1"

busboy@^0.2.11:
  version "0.2.14"
  resolved "https://registry.npmjs.org/busboy/-/busboy-0.2.14.tgz"
  integrity sha1-bCpiLvz0fFe7vh4qnDetNseSVFM=
  dependencies:
    dicer "0.2.5"
    readable-stream "1.1.x"

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.0.tgz"
  integrity sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg==

cacheable-request@^6.0.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/cacheable-request/-/cacheable-request-6.1.0.tgz"
  integrity sha512-Oj3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==
  dependencies:
    clone-response "^1.0.2"
    get-stream "^5.1.0"
    http-cache-semantics "^4.0.0"
    keyv "^3.0.0"
    lowercase-keys "^2.0.0"
    normalize-url "^4.1.0"
    responselike "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

camelcase@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.0.0.tgz"
  integrity sha512-8KMDF1Vz2gzOq54ONPJS65IvTUaB1cHJ2DMM7MbPmLZljDH1qpzzLsWdiN9pHh6qvkRVDTi/07+eNGch/oLU4w==

camelize@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/camelize/-/camelize-1.0.0.tgz"
  integrity sha1-FkpUg+Yw+kMh5a8HAg5TGDGyYJs=

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

catharsis@^0.8.11:
  version "0.8.11"
  resolved "https://registry.npmjs.org/catharsis/-/catharsis-0.8.11.tgz"
  integrity sha512-a+xUyMV7hD1BrDQA/3iPV7oc+6W26BgVJO05PGEoatMyIuPScQKsde6i3YorWX1qs+AZjnJ18NqdKoCtKiNh1g==
  dependencies:
    lodash "^4.17.14"

cfb@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/cfb/-/cfb-1.1.4.tgz"
  integrity sha512-rwFkl3aFO3f+ljR27YINwC0x8vPjyiEVbYbrTCKzspEf7Q++3THdfHVgJYNUbxNcupJECrLX+L40Mjm9hm/Bgw==
  dependencies:
    adler-32 "~1.2.0"
    commander "^2.16.0"
    crc-32 "~1.2.0"
    printj "~1.1.2"

chainsaw@~0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/chainsaw/-/chainsaw-0.1.0.tgz"
  integrity sha1-XqtQsor+WAdNDVgpE4iCi15fvJg=
  dependencies:
    traverse ">=0.3.0 <0.4"

chalk@^2.0.0, chalk@^2.1.0, chalk@^2.4.1:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
  integrity sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.0.tgz"
  integrity sha512-qwx12AxXe2Q5xQ43Ac//I6v5aXTipYrSESdOgzrN+9XjgEpyjpKuvSGaN4qE93f7TQTlerQQ8S+EQ0EyDoVL1A==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
  integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==

check-disk-space@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/check-disk-space/-/check-disk-space-2.1.0.tgz"
  integrity sha512-f0nx9oJF/AVF8nhSYlF1EBvMNnO+CXyLwKhPvN1943iOMI9TWhQigLZm80jAf0wzQhwKkzA8XXjyvuVUeGGcVQ==

chokidar@^3.2.2:
  version "3.4.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.4.0.tgz"
  integrity sha512-aXAaho2VJtisB/1fg1+3nlLJqGOuewTzQpd/Tz0yTg2R0e4IGtshYvtjowyEumcBv2z+y4+kc75Mz7j5xJskcQ==
  dependencies:
    anymatch "~3.1.1"
    braces "~3.0.2"
    glob-parent "~5.1.0"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.4.0"
  optionalDependencies:
    fsevents "~2.1.2"

ci-info@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz"
  integrity sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==

cli-boxes@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/cli-boxes/-/cli-boxes-2.2.0.tgz"
  integrity sha512-gpaBrMAizVEANOpfZp/EEUixTXDyGt7DFzdK5hU+UbWt/J0lB0w20ncZj59Z9a93xHb9u12zF5BS6i9RKbtg4w==

cli-color@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/cli-color/-/cli-color-2.0.0.tgz"
  integrity sha512-a0VZ8LeraW0jTuCkuAGMNufareGHhyZU9z8OGsW0gXd1hZGi1SRuNRXdbGkraBBKnhyUhyebFWnRbp+dIn0f0A==
  dependencies:
    ansi-regex "^2.1.1"
    d "^1.0.1"
    es5-ext "^0.10.51"
    es6-iterator "^2.0.3"
    memoizee "^0.4.14"
    timers-ext "^0.1.7"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
  dependencies:
    restore-cursor "^3.1.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz"
  integrity sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/cliui/-/cliui-5.0.0.tgz"
  integrity sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  integrity sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone-response@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/clone-response/-/clone-response-1.0.2.tgz"
  integrity sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws=
  dependencies:
    mimic-response "^1.0.0"

cockatiel@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/cockatiel/-/cockatiel-1.1.1.tgz"
  integrity sha512-YO02ZhTcK2HOZodksWfg4tS1GYYt1j7R6U1unAkwcQf1uoIYKSBqPrfqXlLNbsyMvkDXMwa2nuwZDHbUkB1VbQ==

codepage@~1.14.0:
  version "1.14.0"
  resolved "https://registry.npmjs.org/codepage/-/codepage-1.14.0.tgz"
  integrity sha1-jL4lSBMjVZ19MHVxsP/5HnodL5k=
  dependencies:
    commander "~2.14.1"
    exit-on-epipe "~1.0.1"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

colors@^1.2.1:
  version "1.4.0"
  resolved "https://registry.npmjs.org/colors/-/colors-1.4.0.tgz"
  integrity sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA==

colors@1.0.x:
  version "1.0.3"
  resolved "https://registry.npmjs.org/colors/-/colors-1.0.3.tgz"
  integrity sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.16.0, commander@~2.17.1:
  version "2.17.1"
  resolved "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz"
  integrity sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==

commander@^2.19.0:
  version "2.20.3"
  resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@~2.14.1:
  version "2.14.1"
  resolved "https://registry.npmjs.org/commander/-/commander-2.14.1.tgz"
  integrity sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw==

commandpost@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/commandpost/-/commandpost-1.4.0.tgz"
  integrity sha512-aE2Y4MTFJ870NuB/+2z1cXBhSBBzRydVVjzhFC4gtenEhpnj15yu0qptWGJsO9YGrcPZ3ezX8AWb1VA391MKpQ==

compress-commons@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/compress-commons/-/compress-commons-3.0.0.tgz"
  integrity sha512-FyDqr8TKX5/X0qo+aVfaZ+PVmNJHJeckFBlq8jZGSJOgnynhfifoyl24qaqdUdDIBe0EVTHByN6NAkqYvE/2Xg==
  dependencies:
    buffer-crc32 "^0.2.13"
    crc32-stream "^3.0.1"
    normalize-path "^3.0.0"
    readable-stream "^2.3.7"

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
  integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.2:
  version "1.6.2"
  resolved "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz"
  integrity sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

configstore@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/configstore/-/configstore-5.0.1.tgz"
  integrity sha512-aMKprgk5YhBNyH25hj8wGt2+D52Sw1DRRIzqBwLp2Ya9mFmY8KPvvtvmna8SxVR9JMZ4kzMD68N22vlaRpkeFA==
  dependencies:
    dot-prop "^5.2.0"
    graceful-fs "^4.1.2"
    make-dir "^3.0.0"
    unique-string "^2.0.0"
    write-file-atomic "^3.0.0"
    xdg-basedir "^4.0.0"

consola@^2.3.0:
  version "2.15.0"
  resolved "https://registry.npmjs.org/consola/-/consola-2.15.0.tgz"
  integrity sha512-vlcSGgdYS26mPf7qNi+dCisbhiyDnrN1zaRbw3CSuc2wGOMEGGPsp46PdRG5gqXwgtJfjxDkxRNAgRPr1B77vQ==

consul@^0.30.0, consul@^0.34.1, consul@^0.37.0:
  version "0.37.0"
  resolved "https://registry.npmjs.org/consul/-/consul-0.37.0.tgz"
  integrity sha512-8V5hUcKK0osd8lShVFEgcYAGaJZmJIYq+sBy/5i4isyJ3Ud159V1PGT2eEvfib7Tu0c9kJ7Uesicc7KEnqj0yA==
  dependencies:
    papi "^0.29.0"

contains-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/contains-path/-/contains-path-0.1.0.tgz"
  integrity sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.3.tgz"
  integrity sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g==
  dependencies:
    safe-buffer "5.1.2"

content-security-policy-builder@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/content-security-policy-builder/-/content-security-policy-builder-2.1.0.tgz"
  integrity sha512-/MtLWhJVvJNkA9dVLAp6fg9LxD2gfI6R2Fi1hPmfjYXSahJJzcfvoeDOxSyp4NvxMuwWv3WMssE9o31DoULHrQ==

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
  integrity sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.0.tgz"
  integrity sha512-+Hp8fLp57wnUSt0tY0tHEXh4voZRDnoIrZPqlo3DPiI4y9lwg/jqx+1Om94/W6ZaPDOUbnjOt/99w66zk+l1Xg==

core-js@~2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/core-js/-/core-js-2.3.0.tgz"
  integrity sha1-+rg/uwstjchfpjbEudNMdUIMbWU=

core-util-is@~1.0.0, core-util-is@1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cors@^2.8.5, cors@2.8.5:
  version "2.8.5"
  resolved "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  integrity sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==
  dependencies:
    object-assign "^4"
    vary "^1"

crc-32@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/crc-32/-/crc-32-1.2.0.tgz"
  integrity sha512-1uBwHxF+Y/4yF5G48fwnKq6QsIXheor3ZLPT80yGBV1oEUwpPojlEhQbWKVw1VwcTQyMGHK1/XMmTjmlsmTTGA==
  dependencies:
    exit-on-epipe "~1.0.1"
    printj "~1.1.0"

crc@^3.4.4:
  version "3.8.0"
  resolved "https://registry.npmjs.org/crc/-/crc-3.8.0.tgz"
  integrity sha512-iX3mfgcTMIq3ZKLIsVFAbv7+Mc10kxabAGQb8HvjA1o3T1PIYprbakQ65d3I+2HGHt6nSKkM9PYjgoJO2KcFBQ==
  dependencies:
    buffer "^5.1.0"

crc32-stream@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/crc32-stream/-/crc32-stream-3.0.1.tgz"
  integrity sha512-mctvpXlbzsvK+6z8kJwSJ5crm7yBwrQMTybJzMw1O4lLGJqjlDCXY2Zw7KheiA6XBEcBmfLx1D88mjRGVJtY9w==
  dependencies:
    crc "^3.4.4"
    readable-stream "^3.4.0"

cross-env@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz"
  integrity sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  integrity sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.0.0.tgz"
  integrity sha512-bzHZN8Pn+gS7DQA6n+iUmBfl0hO5DJq++QP3U6uTucDtk/0iGpXd/Gg7CGR0p8tJhofJyaKoWBuJI4eAO00BBg==

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz"
  integrity sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA==

crypto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/crypto/-/crypto-1.0.1.tgz"
  integrity sha512-VxBKmeNcqQdiUQUW2Tzq0t377b54N2bMtXO/qiLa+6eRRmmC4qT3D4OnTGoT/U6O9aklQ/jTwbOtRMTTY8G0Ig==

cryptr@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/cryptr/-/cryptr-6.0.2.tgz"
  integrity sha512-1TRHI4bmuLIB8WgkH9eeYXzhEg1T4tonO4vVaMBKKde8Dre51J68nAgTVXTwMYXAf7+mV2gBCkm/9wksjSb2sA==

custom-env@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/custom-env/-/custom-env-1.0.2.tgz"
  integrity sha512-PvhrR0te1RtS74fLN+wF8Qr9uCem7FuErK+DSaocIt94/Ic25rqwUVB4sj86Burv4xM19LPBssaXkk6LLm3OEQ==
  dependencies:
    dotenv "*"
    dotenv-expand "^5.0.0"

cycle@~1.0.3, cycle@1.0.x:
  version "1.0.3"
  resolved "https://registry.npmjs.org/cycle/-/cycle-1.0.3.tgz"
  integrity sha1-IegLK+hYD5i0aPN5QwZisEbDStI=

d@^1.0.1, d@1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/d/-/d-1.0.1.tgz"
  integrity sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

dasherize@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/dasherize/-/dasherize-2.0.0.tgz"
  integrity sha1-bYCcnNDPe7iVLYD8hPoT1H3bEwg=

date-format@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/date-format/-/date-format-2.1.0.tgz"
  integrity sha512-bYQuGLeFxhkxNOF3rcMtiZxvCBAquGzZm6oWA1oZ0g2THUzivaRhv8uOhdr19LmoobSOLoIAxeUK2RdbM8IFTA==

date-format@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/date-format/-/date-format-3.0.0.tgz"
  integrity sha512-eyTcpKOcamdhWJXj56DpQMo1ylSQpcGtGKXcU0Tb97+K56/CF5amAqqqNj0+KvA0iw2ynxtHWFsPDSClCxe48w==

dayjs@^1.8.26:
  version "1.8.29"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.8.29.tgz"
  integrity sha512-Vm6teig8ZWK7rH/lxzVGxZJCljPdmUr6q/3f4fr5F0VWNGVkZEjZOQJsAN8hUHUqn+NK4XHNEpJZS1MwLyDcLw==

debug@^2.2.0:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@=3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
  integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
  dependencies:
    ms "2.0.0"

debug@^3.2.6:
  version "3.2.6"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz"
  integrity sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==
  dependencies:
    ms "^2.1.1"

debug@^4.0.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
  dependencies:
    ms "^2.1.1"

debug@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
  dependencies:
    ms "^2.1.1"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@4:
  version "4.1.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
  dependencies:
    ms "^2.1.1"

decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decompress-response@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/decompress-response/-/decompress-response-3.3.0.tgz"
  integrity sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=
  dependencies:
    mimic-response "^1.0.0"

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz"
  integrity sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.2.tgz"
  integrity sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==

defer-to-connect@^1.0.1:
  version "1.1.3"
  resolved "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.1.3.tgz"
  integrity sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ==

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz"
  integrity sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==
  dependencies:
    object-keys "^1.0.12"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

denque@^1.5.0:
  version "1.5.1"
  resolved "https://registry.npmjs.org/denque/-/denque-1.5.1.tgz"
  integrity sha512-XwE+iZ4D6ZUB7mfYRMb5wByE8L74HCn30FBN7sWnXksWc1LO1bPDl67pBR9o/kC4z/xSNAwkMYcGgqDV3BE3Hw==

depd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

deprecate@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/deprecate/-/deprecate-1.1.1.tgz"
  integrity sha512-ZGDXefq1xknT292LnorMY5s8UVU08/WKdzDZCUT6t9JzsiMSP4uzUhgpqugffNVcT5WC6wMBiSQ+LFjlv3v7iQ==

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

dicer@0.2.5:
  version "0.2.5"
  resolved "https://registry.npmjs.org/dicer/-/dicer-0.2.5.tgz"
  integrity sha1-WZbAhrszIYyBLAkL3cCc0S+stw8=
  dependencies:
    readable-stream "1.1.x"
    streamsearch "0.1.2"

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

doctrine@1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-1.5.0.tgz"
  integrity sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

dont-sniff-mimetype@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/dont-sniff-mimetype/-/dont-sniff-mimetype-1.1.0.tgz"
  integrity sha512-ZjI4zqTaxveH2/tTlzS1wFp+7ncxNZaIEWYg3lzZRHkKf5zPT/MnEG6WL0BhHMJUabkh8GeU5NL5j+rEUCb7Ug==

dot-prop@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/dot-prop/-/dot-prop-5.2.0.tgz"
  integrity sha512-uEUyaDKoSQ1M4Oq8l45hSE26SnTxL6snNnqvK/VWx5wJhmff5z0FUVJDKDanor/6w3kzE3i7XZOk+7wC0EXr1A==
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  integrity sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==

dotenv@*, dotenv@^8.2.0:
  version "8.2.0"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-8.2.0.tgz"
  integrity sha512-8sJ78ElpbDJBHNeBzUbUVLsqKdccaa/BXF1uPTw3GrvQTBgrQrtObr2mUrE38vzYd8cEv+m/JBfDLioYcfXoaw==

double-ended-queue@^2.1.0-0:
  version "2.1.0-0"
  resolved "https://registry.npmjs.org/double-ended-queue/-/double-ended-queue-2.1.0-0.tgz"
  integrity sha1-ED01J/0xUo9AGIEwyEHv3XgmTlw=

dtrace-provider@~0.8:
  version "0.8.8"
  resolved "https://registry.npmjs.org/dtrace-provider/-/dtrace-provider-0.8.8.tgz"
  integrity sha512-b7Z7cNtHPhH9EJhNNbbeqTcXB8LGFFZhq1PGgEvpeHlzd36bhbdTWoE/Ba/YguqpBSlAPKnARWhVlhunCMwfxg==
  dependencies:
    nan "^2.14.0"

duplexer2@~0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/duplexer2/-/duplexer2-0.1.4.tgz"
  integrity sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=
  dependencies:
    readable-stream "^2.0.2"

duplexer3@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/duplexer3/-/duplexer3-0.1.4.tgz"
  integrity sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ecdsa-sig-formatter@^1.0.11, ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==
  dependencies:
    safe-buffer "^5.0.1"

editorconfig@^0.15.0:
  version "0.15.3"
  resolved "https://registry.npmjs.org/editorconfig/-/editorconfig-0.15.3.tgz"
  integrity sha512-M9wIMFx96vq0R4F+gRpY3o2exzb8hEj/n9S8unZtHSvYjibBp/iMufSzvmOcV/laG0ZtuTVGtiJggPOSW2r93g==
  dependencies:
    commander "^2.19.0"
    lru-cache "^4.1.5"
    semver "^5.6.0"
    sigmund "^1.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.3.tgz"
  integrity sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

entities@~2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/entities/-/entities-2.0.3.tgz"
  integrity sha512-MyoZ0jgnLvB2X3Lg5HqpFmn1kybDiIfEQmKzTb5apr51Rb+T3KdmMiqa70T+bhGnyv7bQ6WMj2QMHpGMmlrUYQ==

error-ex@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.0, es-abstract@^1.17.0-next.1, es-abstract@^1.17.5:
  version "1.17.6"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.17.6.tgz"
  integrity sha512-Fr89bON3WFyUi5EvAeI48QTWX0AyekGgLA8H+c+7fbfCkJwRWRMLd8CQedNEyJuoYYhmtEqY92pgte1FAhBlhw==
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.0"
    is-regex "^1.1.0"
    object-inspect "^1.7.0"
    object-keys "^1.1.1"
    object.assign "^4.1.0"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es5-ext@^0.10.35, es5-ext@^0.10.45, es5-ext@^0.10.46, es5-ext@^0.10.50, es5-ext@^0.10.51, es5-ext@~0.10.14, es5-ext@~0.10.2, es5-ext@~0.10.46:
  version "0.10.53"
  resolved "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.53.tgz"
  integrity sha512-Xs2Stw6NiNHWypzRTY1MtaG/uJlwCk8kH81920ma8mvN8Xq1gsfhZvpkImLQArw8AHnv8MT2I45J3c0R8slE+Q==
  dependencies:
    es6-iterator "~2.0.3"
    es6-symbol "~3.1.3"
    next-tick "~1.0.0"

es6-iterator@^2.0.3, es6-iterator@~2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-promise@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/es6-promise/-/es6-promise-3.0.2.tgz"
  integrity sha1-AQ1YWEI6XxGJeWZfRkhqlcbuK7Y=

es6-symbol@^3.1.1, es6-symbol@~3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.3.tgz"
  integrity sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA==
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

es6-weak-map@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/es6-weak-map/-/es6-weak-map-2.0.3.tgz"
  integrity sha512-p5um32HOTO1kP+w7PRnB+5lQ43Z6muuMuIMffvDN8ZB4GcnjLBV6zGStpbASIMk4DCAvEaamhe2zhyCb/QXXsA==
  dependencies:
    d "1"
    es5-ext "^0.10.46"
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.1"

escape-goat@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/escape-goat/-/escape-goat-2.1.1.tgz"
  integrity sha512-8/uIhbG12Csjy2JEW7D9pHbreaVaS/OpN3ycnyvElTdwM5n6GY6W6e2IPemfvGZeUMqZ9A/3GqIZMgKnBhAw/Q==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  integrity sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==

eslint-import-resolver-node@^0.3.3:
  version "0.3.4"
  resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.4.tgz"
  integrity sha512-ogtf+5AB/O+nM6DIeBUNr2fuT7ot9Qg/1harBfBtaP13ekEWFQEEMP94BCB7zaNW3gyY+8SHYF00rnqYwXKWOA==
  dependencies:
    debug "^2.6.9"
    resolve "^1.13.1"

eslint-module-utils@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.6.0.tgz"
  integrity sha512-6j9xxegbqe8/kZY8cYpcp0xhbK0EgJlg3g9mib3/miLaExuuwc3n5UEfSnU6hWMbT0FAYVvDbL9RrRgpUeQIvA==
  dependencies:
    debug "^2.6.9"
    pkg-dir "^2.0.0"

eslint-plugin-import@^2.20.0:
  version "2.22.0"
  resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.22.0.tgz"
  integrity sha512-66Fpf1Ln6aIS5Gr/55ts19eUuoDhAbZgnr6UxK5hbDx6l/QgQgx61AePq+BV4PP2uXQFClgMVzep5zZ94qqsxg==
  dependencies:
    array-includes "^3.1.1"
    array.prototype.flat "^1.2.3"
    contains-path "^0.1.0"
    debug "^2.6.9"
    doctrine "1.5.0"
    eslint-import-resolver-node "^0.3.3"
    eslint-module-utils "^2.6.0"
    has "^1.0.3"
    minimatch "^3.0.4"
    object.values "^1.1.1"
    read-pkg-up "^2.0.0"
    resolve "^1.17.0"
    tsconfig-paths "^3.9.0"

eslint-scope@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.0.tgz"
  integrity sha512-iiGRvtxWqgtx5m8EyQUJihBloE4EnYeGE/bz1wSPwJE6tZuJUtHlhqDM4Xj2ukE8Dyy1+HCZ4hE0fzIVMzb58w==
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/eslint-utils/-/eslint-utils-1.4.3.tgz"
  integrity sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  integrity sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==

"eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0", eslint@^6.8.0:
  version "6.8.0"
  resolved "https://registry.npmjs.org/eslint/-/eslint-6.8.0.tgz"
  integrity sha512-K+Iayyo2LtyYhDSYwz5D5QdWw0hCacNzyq1Y821Xna2xSJj7cijoLLYmLxTQgcgZ9mC61nryMy9S7GRbYpI5Ig==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2:
  version "6.2.1"
  resolved "https://registry.npmjs.org/espree/-/espree-6.2.1.tgz"
  integrity sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw==
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esquery@^1.0.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/esquery/-/esquery-1.3.1.tgz"
  integrity sha512-olpvt9QG0vniUBZspVRN6lwB7hOZoTRtT+jzR+tS4ffYx2mzbw+z0XCOk44aaLYKApNX5nMm+E+P6o25ip/DHQ==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz"
  integrity sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ==
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.1.0, estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.1.0.tgz"
  integrity sha512-FyohXK+R0vE+y1nHLoBM7ZTyqRpqAlhdZHCWIWEviFLiGB8b04H6bQs8G+XTthacvT8VuwvteiP7RJSxMs8UEw==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

etcd3@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/etcd3/-/etcd3-1.0.1.tgz"
  integrity sha512-6oOW1LBe24Ts1QDH5AKZNdt5NTLAirEW8+KVcOwPM5rjmKgK64VCiTbMfQ/TufsCNQ+PmnTc1Cxmox5dUhUZlQ==
  dependencies:
    "@grpc/grpc-js" "^1.0.5"
    "@grpc/proto-loader" "^0.5.4"
    bignumber.js "^9.0.0"
    cockatiel "^1.0.0"

event-emitter@^0.3.5:
  version "0.3.5"
  resolved "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

events@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/events/-/events-1.1.1.tgz"
  integrity sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ=

exceljs@^3.8.0:
  version "3.10.0"
  resolved "https://registry.npmjs.org/exceljs/-/exceljs-3.10.0.tgz"
  integrity sha512-c48puhmEvXOBB/FqX0GLkMr7GLOySTHIbEaDSYiSXjSk0UMMAKwWpYT+VNv9IVnRso3d/8gMKF9k0lBbxUdMaw==
  dependencies:
    archiver "^4.0.1"
    dayjs "^1.8.26"
    fast-csv "^4.1.2"
    jszip "^3.4.0"
    proxyquire "^2.1.3"
    readable-stream "^3.6.0"
    saxes "^5.0.1"
    tmp "^0.2.0"
    unzipper "^0.10.11"
    uuid "^7.0.3"

exit-on-epipe@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz"
  integrity sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw==

express@^4.17.1, express@>=4.0.0, express@4.17.1:
  version "4.17.1"
  resolved "https://registry.npmjs.org/express/-/express-4.17.1.tgz"
  integrity sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g==
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

ext@^1.1.2:
  version "1.4.0"
  resolved "https://registry.npmjs.org/ext/-/ext-1.4.0.tgz"
  integrity sha512-Key5NIsUxdqKg3vIsdw9dSuXpPCQ297y6wBjL30edxwPgt2E44WcWBZey/ZvUc6sERLTxKdyCu4gZFmUbk1Q7A==
  dependencies:
    type "^2.0.0"

extend@^3.0.2, extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extsprintf@^1.2.0, extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

eyes@0.1.x:
  version "0.1.8"
  resolved "https://registry.npmjs.org/eyes/-/eyes-0.1.8.tgz"
  integrity sha1-Ys8SAjTGg3hdkCNIqADvPgzCC8A=

fast-csv@^4.1.2:
  version "4.3.1"
  resolved "https://registry.npmjs.org/fast-csv/-/fast-csv-4.3.1.tgz"
  integrity sha512-hCvT77K9fEVvR9iKt8OsOwbci/r1BHn8qcVOghjnllkHaKkpzjrc96IcLHC/2jpuPcPrzXpshk/TOqaRQZK8bA==
  dependencies:
    "@fast-csv/format" "4.3.1"
    "@fast-csv/parse" "4.3.1"
    "@types/node" "^14.0.1"

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-safe-stringify@^2.0.4, fast-safe-stringify@2.0.7:
  version "2.0.7"
  resolved "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.0.7.tgz"
  integrity sha512-Utm6CdzT+6xsDk2m8S6uL8VHxNwI6Jub+e9NYTcAms28T84pTa25GJQV9j0CY0N1rM8hK4x6grpF2BQf+2qwVA==

fast-text-encoding@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/fast-text-encoding/-/fast-text-encoding-1.0.3.tgz"
  integrity sha512-dtm4QZH9nZtcDt8qJiOH9fcQd1NAgi+K1O2DbE6GG1PPCK/BWfOH3idCTRQ4ImXRUOyopDEgDEnVEE7Y/2Wrig==

feature-policy@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/feature-policy/-/feature-policy-0.3.0.tgz"
  integrity sha512-ZtijOTFN7TzCujt1fnNhfWPFPSHeZkesff9AXZj+UEjYBynWNUIYpC87Ve4wHzyexQsImicLu7WsC2LHq7/xrQ==

fecha@^2.3.3:
  version "2.3.3"
  resolved "https://registry.npmjs.org/fecha/-/fecha-2.3.3.tgz"
  integrity sha512-lUGBnIamTAwk4znq5BcqsDaxSmZ9nDVJaij6NvRt/Tg4R69gERA+otPKbS86ROw9nxVMw2/mp1fnaiWqbs6Sdg==

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz"
  integrity sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-5.0.1.tgz"
  integrity sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==
  dependencies:
    flat-cache "^2.0.1"

file-stream-rotator@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/file-stream-rotator/-/file-stream-rotator-0.4.1.tgz"
  integrity sha512-W3aa3QJEc8BS2MmdVpQiYLKHj3ijpto1gMDlsgCRSKfIUe6MwkcpODGPQ3vZfb0XvCeCqlu9CBQTN7oQri2TZQ==
  dependencies:
    moment "^2.11.2"

fill-keys@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/fill-keys/-/fill-keys-1.0.2.tgz"
  integrity sha1-mo+jb06K1jTjv2tPPIiCVRRS6yA=
  dependencies:
    is-object "~1.0.1"
    merge-descriptors "~1.0.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  integrity sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
  integrity sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==
  dependencies:
    locate-path "^3.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-2.0.1.tgz"
  integrity sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0, flatted@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/flatted/-/flatted-2.0.2.tgz"
  integrity sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA==

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz"
  integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
  dependencies:
    debug "=3.1.0"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^2.5.0:
  version "2.5.1"
  resolved "https://registry.npmjs.org/form-data/-/form-data-2.5.1.tgz"
  integrity sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
  integrity sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

frac@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/frac/-/frac-1.1.2.tgz"
  integrity sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz"
  integrity sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz"
  integrity sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fstream@^1.0.12:
  version "1.0.12"
  resolved "https://registry.npmjs.org/fstream/-/fstream-1.0.12.tgz"
  integrity sha512-WvJ193OHa0GHPEL+AycEJgxvBEwyfRkN1vhjca23OaPVMCaLCXTd5qAu82AjTcgP1UJmytkOKb63Ypde7raDIg==
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gaxios@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/gaxios/-/gaxios-3.1.0.tgz"
  integrity sha512-DDTn3KXVJJigtz+g0J3vhcfbDbKtAroSTxauWsdnP57sM5KZ3d2c/3D9RKFJ86s43hfw6WULg6TXYw/AYiBlpA==
  dependencies:
    abort-controller "^3.0.0"
    extend "^3.0.2"
    https-proxy-agent "^5.0.0"
    is-stream "^2.0.0"
    node-fetch "^2.3.0"

gcp-metadata@^4.1.0:
  version "4.1.4"
  resolved "https://registry.npmjs.org/gcp-metadata/-/gcp-metadata-4.1.4.tgz"
  integrity sha512-5J/GIH0yWt/56R3dNaNWPGQ/zXsZOddYECfJaqxFWgrZ9HC2Kvc5vl9upOgUUHKzURjAVf2N+f6tEJiojqXUuA==
  dependencies:
    gaxios "^3.0.0"
    json-bigint "^1.0.0"

gelf@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/gelf/-/gelf-2.0.1.tgz"
  integrity sha512-77duTNPkUgcwFRl+kbmPx2YGMF4e030xBVVsKix5mruTAjuROW36WxmpVSk8MkiZC1D68JYNNb+kfJeZG/D5VQ==
  dependencies:
    async "^2.5.0"

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-stream@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
  integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
  dependencies:
    pump "^3.0.0"

get-stream@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-5.1.0.tgz"
  integrity sha512-EXr1FOzrzTfGeL0gQdeFEvOMm2mzMOglyiOXSTpPC+iAjAKftbr3jpCMWynogwYnM+eSj9sHGc6wjIcDvYiygw==
  dependencies:
    pump "^3.0.0"

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^5.0.0, glob-parent@~5.1.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.1.tgz"
  integrity sha512-FnI+VGOpnlGHWZxthPGR+QhR78fuiK0sNLkHQv+bL9fQi57lNNdquIbna/WrfROrolq8GK5Ek6BiMwqL/voRYQ==
  dependencies:
    is-glob "^4.0.1"

glob@^6.0.1:
  version "6.0.4"
  resolved "https://registry.npmjs.org/glob/-/glob-6.0.4.tgz"
  integrity sha512-MKZeRNyYZAVVVG1oZeLaWie1uweH40m9AZwIwxyPbTSX4hHrVYSzLg0Ro5Z5R7XKkIX+Cc6oD1rqeDJnwsB8/A==
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.0, glob@^7.0.5, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.1.6"
  resolved "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz"
  integrity sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/global-dirs/-/global-dirs-2.0.1.tgz"
  integrity sha512-5HqUqdhkEovj2Of/ms3IeS/EekcO54ytHRLV4PEY2rhRwrHXLQjeVEES0Lhka0xwNDtGYn58wyC4s5+MHsOO6A==
  dependencies:
    ini "^1.3.5"

globals@^12.1.0:
  version "12.4.0"
  resolved "https://registry.npmjs.org/globals/-/globals-12.4.0.tgz"
  integrity sha512-BWICuzzDvDoH54NHKCseDanAhE3CeDorgDL5MT6LMXXj2WCnd9UC2szdk4AWLfjdgNBCXLUanXYcpBBKOSWGwg==
  dependencies:
    type-fest "^0.8.1"

google-auth-library@^6.0.0:
  version "6.0.6"
  resolved "https://registry.npmjs.org/google-auth-library/-/google-auth-library-6.0.6.tgz"
  integrity sha512-fWYdRdg55HSJoRq9k568jJA1lrhg9i2xgfhVIMJbskUmbDpJGHsbv9l41DGhCDXM21F9Kn4kUwdysgxSYBYJUw==
  dependencies:
    arrify "^2.0.0"
    base64-js "^1.3.0"
    ecdsa-sig-formatter "^1.0.11"
    fast-text-encoding "^1.0.0"
    gaxios "^3.0.0"
    gcp-metadata "^4.1.0"
    gtoken "^5.0.0"
    jws "^4.0.0"
    lru-cache "^6.0.0"

google-p12-pem@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/google-p12-pem/-/google-p12-pem-3.0.2.tgz"
  integrity sha512-tbjzndQvSIHGBLzHnhDs3cL4RBjLbLXc2pYvGH+imGVu5b4RMAttUTdnmW2UH0t11QeBTXZ7wlXPS7hrypO/tg==
  dependencies:
    node-forge "^0.9.0"

got@^9.6.0:
  version "9.6.0"
  resolved "https://registry.npmjs.org/got/-/got-9.6.0.tgz"
  integrity sha512-R7eWptXuGYxwijs0eV+v3o6+XH1IqVK8dJOEecQfTmkncw9AV4dcw/Dhxi8MdlqPthxxpZyizMzyg8RTmEsG+Q==
  dependencies:
    "@sindresorhus/is" "^0.14.0"
    "@szmarczak/http-timer" "^1.1.2"
    cacheable-request "^6.0.0"
    decompress-response "^3.3.0"
    duplexer3 "^0.1.4"
    get-stream "^4.1.0"
    lowercase-keys "^1.0.1"
    mimic-response "^1.0.1"
    p-cancelable "^1.0.0"
    to-readable-stream "^1.0.0"
    url-parse-lax "^3.0.0"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.1.9, graceful-fs@^4.2.0, graceful-fs@^4.2.2:
  version "4.2.4"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.4.tgz"
  integrity sha512-WjKPNJF79dtJAVniUlGGWHYGz2jWxT6VhN/4m1NdkbZ2nOsEF+cI1Edgql5zCRhs/VsQYRvrXctxktVXZUkixw==

gtoken@^5.0.0:
  version "5.0.3"
  resolved "https://registry.npmjs.org/gtoken/-/gtoken-5.0.3.tgz"
  integrity sha512-Nyd1wZCMRc2dj/mAD0LlfQLcAO06uKdpKJXvK85SGrF5+5+Bpfil9u/2aw35ltvEHjvl0h5FMKN5knEU+9JrOg==
  dependencies:
    gaxios "^3.0.0"
    google-p12-pem "^3.0.0"
    jws "^4.0.0"
    mime "^2.2.0"

handlebars@^4.1.1, handlebars@^4.5.3:
  version "4.7.6"
  resolved "https://registry.npmjs.org/handlebars/-/handlebars-4.7.6.tgz"
  integrity sha512-1f2BACcBfiwAfStCKZNrUCgqNZkGsAT7UM3kkYtXuLo0KnaVfjKOyf7PRzB6++aK9STyT1Pd2ZCPe3EGOXleXA==
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.0"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz"
  integrity sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-symbols@^1.0.0, has-symbols@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.1.tgz"
  integrity sha512-PLcsoqu++dmEIZB+6totNFKq/7Do+Z0u4oT0zKOJNl3lYK6vGwwu2hjHs+68OEZbTjiUE9bgOABXbP/GvrS0Kg==

has-yarn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/has-yarn/-/has-yarn-2.1.0.tgz"
  integrity sha512-UqBRqi4ju7T+TqGNdqAO0PaSVGsDGJUBQvk9eUWNGRY1CFGDzYhLWoM7JQEemnlvVcv/YEmc2wNW8BC24EnUsw==

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

helmet-crossdomain@0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/helmet-crossdomain/-/helmet-crossdomain-0.4.0.tgz"
  integrity sha512-AB4DTykRw3HCOxovD1nPR16hllrVImeFp5VBV9/twj66lJ2nU75DP8FPL0/Jp4jj79JhTfG+pFI2MD02kWJ+fA==

helmet-csp@2.10.0:
  version "2.10.0"
  resolved "https://registry.npmjs.org/helmet-csp/-/helmet-csp-2.10.0.tgz"
  integrity sha512-Rz953ZNEFk8sT2XvewXkYN0Ho4GEZdjAZy4stjiEQV3eN7GDxg1QKmYggH7otDyIA7uGA6XnUMVSgeJwbR5X+w==
  dependencies:
    bowser "2.9.0"
    camelize "1.0.0"
    content-security-policy-builder "2.1.0"
    dasherize "2.0.0"

helmet@^3.21.2:
  version "3.23.3"
  resolved "https://registry.npmjs.org/helmet/-/helmet-3.23.3.tgz"
  integrity sha512-U3MeYdzPJQhtvqAVBPntVgAvNSOJyagwZwyKsFdyRa8TV3pOKVFljalPOCxbw5Wwf2kncGhmP0qHjyazIdNdSA==
  dependencies:
    depd "2.0.0"
    dont-sniff-mimetype "1.1.0"
    feature-policy "0.3.0"
    helmet-crossdomain "0.4.0"
    helmet-csp "2.10.0"
    hide-powered-by "1.1.0"
    hpkp "2.0.0"
    hsts "2.2.0"
    nocache "2.1.0"
    referrer-policy "1.2.0"
    x-xss-protection "1.3.0"

hide-powered-by@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/hide-powered-by/-/hide-powered-by-1.1.0.tgz"
  integrity sha512-Io1zA2yOA1YJslkr+AJlWSf2yWFkKjvkcL9Ni1XSUqnGLr/qRQe2UI3Cn/J9MsJht7yEVCe0SscY1HgVMujbgg==

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.8.tgz"
  integrity sha512-f/wzC2QaWBs7t9IYqB4T3sR1xviIViXJRJTWBlx2Gf3g0Xi5vI7Yy4koXQ1c9OYDGHN9sBy1DQ2AB8fqZBWhUg==

hpkp@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/hpkp/-/hpkp-2.0.0.tgz"
  integrity sha1-EOFCJk52IVpdMMROxD3mTe5tFnI=

hsts@2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/hsts/-/hsts-2.2.0.tgz"
  integrity sha512-ToaTnQ2TbJkochoVcdXYm4HOCliNozlviNsg+X2XQLQvZNI/kCHR9rZxVYpJB3UPcHz80PgxRyWQ7PdU1r+VBQ==
  dependencies:
    depd "2.0.0"

http-cache-semantics@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.0.tgz"
  integrity sha512-carPklcUh7ROWRK7Cv27RPtdhYhUsela/ue5/jKzjegVvXDqM2ILE9Q2BGn9JZJh1g87cp56su/FgQSzcWS8cQ==

http-errors@~1.7.2, http-errors@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.7.2.tgz"
  integrity sha512-uUQBt3H/cSIVfch6i1EuPNy/YsRSOUBXTVfZ+yR7Zjez3qjBz6i9+i4zjNaoqcoFVI4lQJ5plg63TvGfRSDCRg==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.0.tgz"
  integrity sha512-EkYm5BcKUGiduxzSt3Eppko+PiNWNEpa4ySk9vTC6wDsQJW9rHSa+UhGNJoRYp7bz6Ht1eaRIa6QaJqO5rCFbA==
  dependencies:
    agent-base "6"
    debug "4"

iconv-lite@^0.4.24, iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.4, ieee754@1.1.13:
  version "1.1.13"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

ignore-by-default@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz"
  integrity sha1-SMptcvbGo68Aqa1K5odr44ieKwk=

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz"
  integrity sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==

immediate@~3.0.5:
  version "3.0.6"
  resolved "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  integrity sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=

import-fresh@^3.0.0:
  version "3.2.1"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.2.1.tgz"
  integrity sha512-6e1q1cnWP2RXD9/keSkxHScg508CdXqXWgWBaETNhyuBFz+kUZlKboh+ISK+bU++DmbHimVBrOz/zzPe0sZ3sQ==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/import-lazy/-/import-lazy-2.1.0.tgz"
  integrity sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3, inherits@2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.5, ini@~1.3.0:
  version "1.3.5"
  resolved "https://registry.npmjs.org/ini/-/ini-1.3.5.tgz"
  integrity sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw==

inquirer@^7.0.0:
  version "7.3.0"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-7.3.0.tgz"
  integrity sha512-K+LZp6L/6eE5swqIcVXrxl21aGDU4S50gKH0/d96OMQnSBCyGyZl/oZhbkVmdp5sBoINHd4xZvFSARh2dk6DWA==
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.15"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz"
  integrity sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-callable@^1.1.4, is-callable@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.0.tgz"
  integrity sha512-pyVD9AaGLxtg6srb2Ng6ynWJqkHU9bEM087AKck0w8QwDarTfNcpIYoU8x8Hv2Icm8u6kFJM18Dag8lyqGkviw==

is-ci@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-ci/-/is-ci-2.0.0.tgz"
  integrity sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==
  dependencies:
    ci-info "^2.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.2.tgz"
  integrity sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz"
  integrity sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg==
  dependencies:
    is-extglob "^2.1.1"

is-installed-globally@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-installed-globally/-/is-installed-globally-0.3.2.tgz"
  integrity sha512-wZ8x1js7Ia0kecP/CHM/3ABkAmujX7WPvQk6uu3Fly/Mk44pySulQpnHG46OMjHGXApINnV4QhY3SWnECO2z5g==
  dependencies:
    global-dirs "^2.0.1"
    is-path-inside "^3.0.1"

is-npm@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/is-npm/-/is-npm-4.0.0.tgz"
  integrity sha512-96ECIfh9xtDDlPylNPXhzjsykHsMJZ18ASpaWzQyBr4YRTcVjUvzaHayDAES2oU/3KpljhHUjtSRNiDwi0F0ig==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz"
  integrity sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==

is-object@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-object/-/is-object-1.0.1.tgz"
  integrity sha1-iVJojF7C/9awPsyF52ngKQMINHA=

is-path-inside@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.2.tgz"
  integrity sha512-/2UGPSgmtqwo1ktx8NDHjuPwZWmHhO+gj0f93EkhLB5RgW9RZevWYYlIkS6zePc6U2WpOdQYIwHe9YC4DWEBVg==

is-plain-object@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-3.0.1.tgz"
  integrity sha512-Xnpx182SBMrr/aBik8y+GuR4U1L9FqMSojwDQwPMmxyC6bvEqly9UBCxhauBF5vNh2gwWJNX6oDV7O+OM4z34g==

is-promise@^2.1:
  version "2.2.2"
  resolved "https://registry.npmjs.org/is-promise/-/is-promise-2.2.2.tgz"
  integrity sha512-+lP4/6lKUBfQjZ2pdxThZvLUAafmZb8OAxFb8XXtiQmS35INgr85hdOGoEs124ez1FCnZJt6jau/T+alh58QFQ==

is-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.0.tgz"
  integrity sha512-iI97M8KTWID2la5uYXlkbSDQIg4F6o1sYboZKKTDpnDQMLtUL86zxhgDet3Q2SriaYsyGqZ6Mn2SjbRKeLHdqw==
  dependencies:
    has-symbols "^1.0.1"

is-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.0.tgz"
  integrity sha512-XCoy+WlUr7d1+Z8GgSuXmpuUFC9fOhRXglJMx+dwLKTkL44Cjd4W1Z5P+BQZpr+cR93aGP4S/s7Ftw6Nd/kiEw==

is-string@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.5.tgz"
  integrity sha512-buY6VNRjhQMiF1qWDouloZlQbRhDPCebwxSjxMjxgemYT46YMd2NR0/H+fBhEfWX4A/w9TBJ+ol+okqJKFE6vQ==

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.3.tgz"
  integrity sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ==
  dependencies:
    has-symbols "^1.0.1"

is-typedarray@^1.0.0, is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-yarn-global@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/is-yarn-global/-/is-yarn-global-0.3.0.tgz"
  integrity sha512-VjSeb/lHmkoyd8ryPVIKvOCn4D1koMqY+vqyjjUfc3xyKtP4dYOxM44sZrnqQSzSds3xyOrUTLTC9LVCVgLngw==

isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isomorphic-ws@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/isomorphic-ws/-/isomorphic-ws-4.0.1.tgz"
  integrity sha512-BhBvN2MBpWTaSHdWRb/bwdZJ1WaehQ2L1KngkCkfLUGF0mAWAT1sQUQacEmQ0jXkFw/czDXPNQSL5u2/Krsz1w==

isstream@~0.1.2, isstream@0.1.x:
  version "0.1.2"
  resolved "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

iterare@1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/iterare/-/iterare-1.2.1.tgz"
  integrity sha512-RKYVTCjAnRthyJes037NX/IiqeidgN1xc3j1RjFfECFp28A1GVwK9nA+i0rJPaHqSZwygLzRnFlzUuHFoWWy+Q==

jmespath@0.15.0:
  version "0.15.0"
  resolved "https://registry.npmjs.org/jmespath/-/jmespath-0.15.0.tgz"
  integrity sha1-o/Iiqarp+Wb10nx5ZRDigJF2Qhc=

jose@^1.27.1:
  version "1.28.0"
  resolved "https://registry.npmjs.org/jose/-/jose-1.28.0.tgz"
  integrity sha512-JmfDRzt/HSj8ipd9TsDtEHoLUnLYavG+7e8F6s1mx2jfVSfXOTaFQsJUydbjJpTnTDHP1+yKL9Ke7ktS/a0Eiw==
  dependencies:
    "@panva/asn1.js" "^1.0.0"

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^3.13.1:
  version "3.14.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.0.tgz"
  integrity sha512-/4IbIeHcD9VMHFqDR/gQ7EdZdLimOvW2DdcxFjdyyZ9NsbS+ccrXqVWDtab/lRl5AlUqmpBx8EhPaWR+OtY17A==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js2xmlparser@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/js2xmlparser/-/js2xmlparser-4.0.1.tgz"
  integrity sha512-KrPTolcw6RocpYjdC7pL7v62e55q7qOMHvLX1UCLc5AAS8qeJ6nukarEJAF2KL2PZxlbGueEbINqZR2bDe/gUw==
  dependencies:
    xmlcreate "^2.0.3"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdoc@^3.6.3:
  version "3.6.4"
  resolved "https://registry.npmjs.org/jsdoc/-/jsdoc-3.6.4.tgz"
  integrity sha512-3G9d37VHv7MFdheviDCjUfQoIjdv4TC5zTTf5G9VODLtOnVS6La1eoYBDlbWfsRT3/Xo+j2MIqki2EV12BZfwA==
  dependencies:
    "@babel/parser" "^7.9.4"
    bluebird "^3.7.2"
    catharsis "^0.8.11"
    escape-string-regexp "^2.0.0"
    js2xmlparser "^4.0.1"
    klaw "^3.0.0"
    markdown-it "^10.0.0"
    markdown-it-anchor "^5.2.7"
    marked "^0.8.2"
    mkdirp "^1.0.4"
    requizzle "^0.2.3"
    strip-json-comments "^3.1.0"
    taffydb "2.6.2"
    underscore "~1.10.2"

json-bigint@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz"
  integrity sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==
  dependencies:
    bignumber.js "^9.0.0"

json-buffer@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.0.tgz"
  integrity sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-stream/-/json-stream-1.0.0.tgz"
  integrity sha1-GjhU4o0rvuqzHMfd9oPS3cVlJwg=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz"
  integrity sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==
  dependencies:
    minimist "^1.2.0"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonpath-plus@^0.19.0:
  version "0.19.0"
  resolved "https://registry.npmjs.org/jsonpath-plus/-/jsonpath-plus-0.19.0.tgz"
  integrity sha512-GSVwsrzW9LsA5lzsqe4CkuZ9wp+kxBb2GwNniaWzI2YFn5Ig42rSW8ZxVpWXaAfakXNrx5pgY5AbQq7kzX29kg==

jsonwebtoken@^9.0.1:
  version "9.0.1"
  resolved "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.1.tgz"
  integrity sha512-K8wx7eJ5TPvEjuiVSkv167EVboBDv9PZdDoF7BgeQnBLVvZWW9clr2PsQHVJDTKaEIH5JBIwHujGcHp7GgI2eg==
  dependencies:
    jws "^3.2.2"
    lodash "^4.17.21"
    ms "^2.1.1"
    semver "^7.3.8"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

jszip@^3.4.0:
  version "3.5.0"
  resolved "https://registry.npmjs.org/jszip/-/jszip-3.5.0.tgz"
  integrity sha512-WRtu7TPCmYePR1nazfrtuF216cIVon/3GWOvHS9QR5bIwSbnxtdpma6un3jyGGNhHsKCSzn5Ypk+EkDRvTGiFA==
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    set-immediate-shim "~1.0.1"

jszip@3.1.5:
  version "3.1.5"
  resolved "https://registry.npmjs.org/jszip/-/jszip-3.1.5.tgz"
  integrity sha512-5W8NUaFRFRqTOL7ZDDrx5qWHJyBXy6velVudIzQUSoqAAYqzSh2Z7/m0Rf1QbmQJccegD0r+YZxBjzqoBiEeJQ==
  dependencies:
    core-js "~2.3.0"
    es6-promise "~3.0.2"
    lie "~3.1.0"
    pako "~1.0.2"
    readable-stream "~2.0.6"

jwa@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz"
  integrity sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jwa@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/jwa/-/jwa-2.0.0.tgz"
  integrity sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz"
  integrity sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

jws@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/jws/-/jws-4.0.0.tgz"
  integrity sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==
  dependencies:
    jwa "^2.0.0"
    safe-buffer "^5.0.1"

jwt-decode@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/jwt-decode/-/jwt-decode-3.1.2.tgz"
  integrity sha512-UfpWE/VZn0iP50d8cz9NrZLM9lSWhcJ+0Gt/nm4by88UL+J1SiKN8/5dkjMmbEzwL2CAe+67GsegCbIKtbp75A==

keyv@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/keyv/-/keyv-3.1.0.tgz"
  integrity sha512-9ykJ/46SN/9KPM/sichzQ7OvXyGDYKGTaDlKMGCAlg2UK8KRy4jb0d8sFc+0Tt0YYnThq8X2RZgCg74RPxgcVA==
  dependencies:
    json-buffer "3.0.0"

klaw@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/klaw/-/klaw-3.0.0.tgz"
  integrity sha512-0Fo5oir+O9jnXu5EefYbVK+mHMBeEVEy2cmctR1O1NECcCkPRreJKrS6Qt/j3KC2C148Dfo9i3pCmCMsdqGr0g==
  dependencies:
    graceful-fs "^4.1.9"

kubernetes-client@^8.3.3:
  version "8.3.7"
  resolved "https://registry.npmjs.org/kubernetes-client/-/kubernetes-client-8.3.7.tgz"
  integrity sha512-A0rvfQAvwAuPTooBOSErpTcnwcQxhkmawjOm/gUdGDWCUZoYmAVgVGFnc/klda+X1tvHwleavDsLqmqaYscH2w==
  dependencies:
    "@kubernetes/client-node" "0.10.2"
    camelcase "^6.0.0"
    deepmerge "^4.2.2"
    depd "^2.0.0"
    js-yaml "^3.13.1"
    json-stream "^1.0.0"
    openid-client "^3.14.0"
    pump "^3.0.0"
    qs "^6.9.0"
    request "^2.88.2"
    swagger-fluent "^5.0.1"
    url-join "^4.0.1"
    ws "^7.2.3"

latest-version@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/latest-version/-/latest-version-5.1.0.tgz"
  integrity sha512-weT+r0kTkRQdCdYCNtkMwWXQTMEswKrFBkm4ckQOMVhhqhIMI1UT2hMj+1iigIhgSZm5gTmrRXBNoGUgaTY1xA==
  dependencies:
    package-json "^6.3.0"

lazystream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/lazystream/-/lazystream-1.0.0.tgz"
  integrity sha1-9plf4PggOS9hOWvolGJAe7dxaOQ=
  dependencies:
    readable-stream "^2.0.5"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lie@~3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/lie/-/lie-3.1.1.tgz"
  integrity sha1-mkNrLMd0bKWd56QfpGmz77dr2H4=
  dependencies:
    immediate "~3.0.5"

lie@~3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  integrity sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==
  dependencies:
    immediate "~3.0.5"

linkify-it@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/linkify-it/-/linkify-it-2.2.0.tgz"
  integrity sha512-GnAl/knGn+i1U/wjBz3akz2stz+HrHLsxMwHQGofCDfPvlf+gDKN58UtfmUquTY4/MXeE2x7k19KQmeoZi94Iw==
  dependencies:
    uc.micro "^1.0.1"

listenercount@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/listenercount/-/listenercount-1.0.1.tgz"
  integrity sha1-hMinKrWcRyUyFIDJdeZQg0LnCTc=

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/load-json-file/-/load-json-file-2.0.0.tgz"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
  integrity sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.difference@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.difference/-/lodash.difference-4.5.0.tgz"
  integrity sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw=

lodash.escaperegexp@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/lodash.escaperegexp/-/lodash.escaperegexp-4.1.2.tgz"
  integrity sha1-ZHYsSGGAglGKw99Mz11YhtriA0c=

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/lodash.flatten/-/lodash.flatten-4.4.0.tgz"
  integrity sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=

lodash.groupby@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash.groupby/-/lodash.groupby-4.6.0.tgz"
  integrity sha1-Cwih3PaDl8OXhVwyOXg4Mt90A9E=

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  integrity sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
  integrity sha1-QVxEePK8wwEgwizhDtMib30+GOA=

lodash.isfunction@^3.0.9:
  version "3.0.9"
  resolved "https://registry.npmjs.org/lodash.isfunction/-/lodash.isfunction-3.0.9.tgz"
  integrity sha512-AirXNj15uRIMMPihnkInB4i3NHeb4iBtNg9WRWuK2o31S+ePwwNmDPaTL3o7dTJ+VXNZim7rFs4rxN4YU1oUJw==

lodash.isnil@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/lodash.isnil/-/lodash.isnil-4.0.0.tgz"
  integrity sha1-SeKM1VkBNFjIFMVHnTxmOiG/qmw=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.isundefined@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/lodash.isundefined/-/lodash.isundefined-3.0.1.tgz"
  integrity sha1-I+89lTVWUgOmbO/VuDD4SJEa+0g=

lodash.union@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash.union/-/lodash.union-4.6.0.tgz"
  integrity sha1-SLtQiECfFvGCFmZkHETdGqrjzYg=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15:
  version "4.17.17"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.17.tgz"
  integrity sha512-/B2DjOphAoqi5BX4Gg2oh4UR0Gy/A7xYAMh3aSECEKzwS3eCDEpS0Cals1Ktvxwlal3bBJNc+5W9kNIcADdw5Q==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

lodash@4.17.20:
  version "4.17.20"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.20.tgz"
  integrity sha512-PlhdFcillOINfeV7Ni6oF1TAEayyZBoZ8bcshTHqOYJYlrqzRK5hagpagky5o4HfCzzd1TRkXPMFq6cKk9rGmA==

log4js@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/log4js/-/log4js-6.3.0.tgz"
  integrity sha512-Mc8jNuSFImQUIateBFwdOQcmC6Q5maU0VVvdC2R6XMb66/VnT+7WS4D/0EeNMZu1YODmJe5NIn2XftCzEocUgw==
  dependencies:
    date-format "^3.0.0"
    debug "^4.1.1"
    flatted "^2.0.1"
    rfdc "^1.1.4"
    streamroller "^2.2.4"

logform@^1.6.0:
  version "1.10.0"
  resolved "https://registry.npmjs.org/logform/-/logform-1.10.0.tgz"
  integrity sha512-em5ojIhU18fIMOw/333mD+ZLE2fis0EzXl1ZwHx4iQzmpQi6odNiY/t+ITNr33JZhT9/KEaH+UPIipr6a9EjWg==
  dependencies:
    colors "^1.2.1"
    fast-safe-stringify "^2.0.4"
    fecha "^2.3.3"
    ms "^2.1.1"
    triple-beam "^1.2.0"

long@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/long/-/long-4.0.0.tgz"
  integrity sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA==

lowercase-keys@^1.0.0, lowercase-keys@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-1.0.1.tgz"
  integrity sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA==

lowercase-keys@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-2.0.0.tgz"
  integrity sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==

lru-cache@^4.1.5:
  version "4.1.5"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz"
  integrity sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

lru-queue@0.1:
  version "0.1.0"
  resolved "https://registry.npmjs.org/lru-queue/-/lru-queue-0.1.0.tgz"
  integrity sha1-Jzi9nw089PhEkMVzbEhpmsYyzaM=
  dependencies:
    es5-ext "~0.10.2"

make-dir@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

make-error@^1.1.1, make-error@^1.3.6:
  version "1.3.6"
  resolved "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  integrity sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==

markdown-it-anchor@^5.2.7:
  version "5.3.0"
  resolved "https://registry.npmjs.org/markdown-it-anchor/-/markdown-it-anchor-5.3.0.tgz"
  integrity sha512-/V1MnLL/rgJ3jkMWo84UR+K+jF1cxNG1a+KwqeXqTIJ+jtA8aWSHuigx8lTzauiIjBDbwF3NcWQMotd0Dm39jA==

markdown-it@*, markdown-it@^10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/markdown-it/-/markdown-it-10.0.0.tgz"
  integrity sha512-YWOP1j7UbDNz+TumYP1kpwnP0aEa711cJjrAQrzd0UXlbJfc5aAq0F/PZHjiioqDC1NKgvIMX+o+9Bk7yuM2dg==
  dependencies:
    argparse "^1.0.7"
    entities "~2.0.0"
    linkify-it "^2.0.0"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

marked@^0.8.2:
  version "0.8.2"
  resolved "https://registry.npmjs.org/marked/-/marked-0.8.2.tgz"
  integrity sha512-EGwzEeCcLniFX51DhTpmTom+dSA/MG/OBUDjnWtHbEnjAH180VzUeAw+oE4+Zv+CoYBWyRlYOTR0N8SO9R1PVw==

mdurl@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/mdurl/-/mdurl-1.0.1.tgz"
  integrity sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memoizee@^0.4.14:
  version "0.4.14"
  resolved "https://registry.npmjs.org/memoizee/-/memoizee-0.4.14.tgz"
  integrity sha512-/SWFvWegAIYAO4NQMpcX+gcra0yEZu4OntmUdrBaWrJncxOqAziGFlHxc7yjKVK2uu3lpPW27P27wkR82wA8mg==
  dependencies:
    d "1"
    es5-ext "^0.10.45"
    es6-weak-map "^2.0.2"
    event-emitter "^0.3.5"
    is-promise "^2.1"
    lru-queue "0.1"
    next-tick "1"
    timers-ext "^0.1.5"

merge-descriptors@~1.0.0, merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/merge/-/merge-1.2.1.tgz"
  integrity sha512-VjFo4P5Whtj4vsLzsYBu5ayHhoHJ0UqNm7ibvShmbmoz7tGi0vXaoJbGdB+GmDMLUdg8DpQXEIeVDAe8MaABvQ==

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

"mime-db@>= 1.43.0 < 2", mime-db@1.44.0:
  version "1.44.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.44.0.tgz"
  integrity sha512-/NOTfLrsPBVeH7YtFPgsVWveuL+4SjjYxaQ1xtM1KMFj7HdxlBlxeyNLzhyJVx7r4rZGJAZ/6lkKCitSc/Nmpg==

mime-types@^2.1.12, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.27"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.27.tgz"
  integrity sha512-JIhqnCasI9yD+SsmkquHBxTSEuZdQX5BuQnS2Vc7puQQQ+8yiP5AY5uWhpdv4YL4VM5c6iliiYWPgJ/nJQLp7w==
  dependencies:
    mime-db "1.44.0"

mime@^2.2.0:
  version "2.4.6"
  resolved "https://registry.npmjs.org/mime/-/mime-2.4.6.tgz"
  integrity sha512-RZKhC3EmpBchfTGBVb8fb+RL2cWyw/32lshnsETttkBAyAUXSGHxbEJWWRXc751DrIxG1q04b8QwMbAwkRPpUA==

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-response@^1.0.0, mimic-response@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.1.tgz"
  integrity sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==

minimatch@^3.0.4, "minimatch@2 || 3":
  version "3.0.4"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  integrity sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz"
  integrity sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==

mkdirp@^0.5.1, "mkdirp@>=0.5 0", mkdirp@~0.5.1:
  version "0.5.5"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz"
  integrity sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==
  dependencies:
    minimist "^1.2.5"

mkdirp@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

module-not-found-error@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/module-not-found-error/-/module-not-found-error-1.0.1.tgz"
  integrity sha1-z4tP9PKWQGdNbN0CsOO8UjwrvcA=

moment@*, moment@^2.11.2, moment@^2.19.3, moment@^2.24.0:
  version "2.27.0"
  resolved "https://registry.npmjs.org/moment/-/moment-2.27.0.tgz"
  integrity sha512-al0MUK7cpIcglMv3YF13qSgdAIqxHTO7brRtaz3DlSULbqfazqkc5kEjNrLDOM7fsjshoFIihnU8snrP7zUvhQ==

ms@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz"
  integrity sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==

multer@1.4.2:
  version "1.4.2"
  resolved "https://registry.npmjs.org/multer/-/multer-1.4.2.tgz"
  integrity sha512-xY8pX7V+ybyUpbYMxtjM9KAiD9ixtg5/JkeKUTD6xilfDv0vzzOFcCp4Ljb1UU3tSOM3VTZtKo63OmzOrGi3Cg==
  dependencies:
    append-field "^1.0.0"
    busboy "^0.2.11"
    concat-stream "^1.5.2"
    mkdirp "^0.5.1"
    object-assign "^4.1.1"
    on-finished "^2.3.0"
    type-is "^1.6.4"
    xtend "^4.0.0"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz"
  integrity sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==

mv@~2:
  version "2.1.1"
  resolved "https://registry.npmjs.org/mv/-/mv-2.1.1.tgz"
  integrity sha512-at/ZndSy3xEGJ8i0ygALh8ru9qy7gWW1cmkaqBN29JmMlIvM//MEO9y1sk/avxuwnPcfhkejkLsuPxH81BrkSg==
  dependencies:
    mkdirp "~0.5.1"
    ncp "~2.0.0"
    rimraf "~2.4.0"

nan@^2.14.0:
  version "2.18.0"
  resolved "https://registry.npmjs.org/nan/-/nan-2.18.0.tgz"
  integrity sha512-W7tfG7vMOGtD30sHoZSSc/JVYiyDPEyQVso/Zz+/uQd0B0L46gtC+pHha5FFMRpil6fm/AoEcRWyOVi4+E/f8w==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

ncp@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ncp/-/ncp-2.0.0.tgz"
  integrity sha512-zIdGUrPRFTUELUvr3Gmc7KZ2Sw/h1PiVM0Af/oHB6zgnV1ikqSfRk+TOufi79aHYCW3NiOXmr1BP5nWbzojLaA==

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz"
  integrity sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==

neo-async@^2.6.0:
  version "2.6.1"
  resolved "https://registry.npmjs.org/neo-async/-/neo-async-2.6.1.tgz"
  integrity sha512-iyam8fBuCUpWeKPGpaNMetEocMt364qkCsfL9JuhjXX6dRnguRVOfk2GZaDpPjcOKiiXCPINZC1GczQ7iTq3Zw==

next-tick@~1.0.0, next-tick@1:
  version "1.0.0"
  resolved "https://registry.npmjs.org/next-tick/-/next-tick-1.0.0.tgz"
  integrity sha1-yobR/ogoFpsBICCOPchCS524NCw=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz"
  integrity sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==

nocache@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/nocache/-/nocache-2.1.0.tgz"
  integrity sha512-0L9FvHG3nfnnmaEQPjT9xhfN4ISk0A8/2j4M37Np4mcDesJjHgEUfgPhdCyZuFI954tjokaIj/A3NdpFNdEh4Q==

node-fetch@^2.3.0, node-fetch@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.0.tgz"
  integrity sha512-8dG4H5ujfvFiqDmVu9fQ5bOHUC15JMjMY/Zumv26oOvvVJjM67KF8koCWIabKQ1GJIa9r2mMZscBq/TbdOcmNA==

node-forge@^0.9.0:
  version "0.9.1"
  resolved "https://registry.npmjs.org/node-forge/-/node-forge-0.9.1.tgz"
  integrity sha512-G6RlQt5Sb4GMBzXvhfkeFmbqR6MzhtnT7VTHuLadjkii3rdYHNdw0m8zA4BTxVIh68FicCQ2NSUANpsqkr9jvQ==

nodemon@^2.0.2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/nodemon/-/nodemon-2.0.4.tgz"
  integrity sha512-Ltced+hIfTmaS28Zjv1BM552oQ3dbwPqI4+zI0SLgq+wpJhSyqgYude/aZa/3i31VCQWMfXJVxvu86abcam3uQ==
  dependencies:
    chokidar "^3.2.2"
    debug "^3.2.6"
    ignore-by-default "^1.0.1"
    minimatch "^3.0.4"
    pstree.remy "^1.1.7"
    semver "^5.7.1"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.2"
    update-notifier "^4.0.0"

nopt@~1.0.10:
  version "1.0.10"
  resolved "https://registry.npmjs.org/nopt/-/nopt-1.0.10.tgz"
  integrity sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2:
  version "2.5.0"
  resolved "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  integrity sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-url@^4.1.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-4.5.0.tgz"
  integrity sha512-2s47yzUxdexf1OhyRi4Em83iQk0aPvwTddtFz4hnSSw9dCEsLEGf6SwIO8ss/19S9iBb5sJaOuTvTGDeZI00BQ==

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
  integrity sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==

object-assign@^4, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-1.3.1.tgz"
  integrity sha512-OSuu/pU4ENM9kmREg0BdNrUDIl1heYa4mBZacJc+vVWz4GtAwu7jO8s4AIt2aGRUTqxykpWzI3Oqnsm13tTMDA==

object-hash@^2.0.1, object-hash@2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-2.0.3.tgz"
  integrity sha512-JPKn0GMu+Fa3zt3Bmr66JhokJU5BaNBIh4ZeTlaCBzrBsOeXzwcKKAK1tbLiPKgvwmPXsDvvLHoWh5Bm7ofIYg==

object-inspect@^1.7.0:
  version "1.8.0"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.8.0.tgz"
  integrity sha512-jLdtEOB112fORuypAyl/50VRVIBIdVQOSUUGQHzJ4xBSbit81zRarz7GThkEFZy1RceYrWYcPcBFPQwHyAc1gA==

object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.0.tgz"
  integrity sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w==
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.values@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object.values/-/object.values-1.1.1.tgz"
  integrity sha512-WTa54g2K8iu0kmS/us18jEmdv1a4Wi//BZ/DTVYEcH0XhLM5NYdpDHja3gt57VrZLcNAO2WGA+KpWsDBaHt6eA==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"
    function-bind "^1.1.1"
    has "^1.0.3"

oidc-token-hash@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/oidc-token-hash/-/oidc-token-hash-5.0.0.tgz"
  integrity sha512-8Yr4CZSv+Tn8ZkN3iN2i2w2G92mUKClp4z7EGUfdsERiYSbj7P4i/NHm72ft+aUdsiFx9UdIPSTwbyzQ6C4URg==

on-finished@^2.3.0, on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.0.tgz"
  integrity sha512-5NcSkPHhwTVFIQN+TUqXoS5+dlElHXdpAWu9I0HP20YOtIi+aZ0Ct82jdlILDxjLEAWwvm+qj1m6aEtsDVmm6Q==
  dependencies:
    mimic-fn "^2.1.0"

openid-client@^3.14.0:
  version "3.15.9"
  resolved "https://registry.npmjs.org/openid-client/-/openid-client-3.15.9.tgz"
  integrity sha512-CNKMxM1oj4+JIWi/TQ4kGQuG2l5S/5yvSZ1wzz1fg+/fhD/MWRmqRB8GHW0EV+fbbSncDx9Lhoq04mu7S0/fbw==
  dependencies:
    "@types/got" "^9.6.9"
    base64url "^3.0.1"
    got "^9.6.0"
    jose "^1.27.1"
    lru-cache "^6.0.0"
    make-error "^1.3.6"
    object-hash "^2.0.1"
    oidc-token-hash "^5.0.0"
    p-any "^3.0.0"

optionator@^0.8.3:
  version "0.8.3"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
  integrity sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

oracledb@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/oracledb/-/oracledb-5.0.0.tgz"
  integrity sha512-NLE3t6KiAkpBHA1/zgjNiKaa9Z4Nnp4PuB3d0b3Kz4C8klrIrMKfHIGUySlwqgDW588m7/2hnPxU7PH6wjBd6g==

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-any@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/p-any/-/p-any-3.0.0.tgz"
  integrity sha512-5rqbqfsRWNb0sukt0awwgJMlaep+8jV45S15SKKB34z4UuzjcofIfnriCBhWjZP2jbVtjt9yRl7buB6RlKsu9w==
  dependencies:
    p-cancelable "^2.0.0"
    p-some "^5.0.0"

p-cancelable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/p-cancelable/-/p-cancelable-1.1.0.tgz"
  integrity sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw==

p-cancelable@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/p-cancelable/-/p-cancelable-2.0.0.tgz"
  integrity sha512-wvPXDmbMmu2ksjkB4Z3nZWTSkJEb9lqVdMaCKpZUGJG9TMiNp9XcbG3fn9fPKjem04fJMJnXoyFPk2FmgiaiNg==

p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz"
  integrity sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
  integrity sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-some@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/p-some/-/p-some-5.0.0.tgz"
  integrity sha512-Js5XZxo6vHjB9NOYAzWDYAIyyiPvva0DWESAIWIK7uhSpGsyg5FwUPxipU/SOQx5x9EqhOh545d1jo6cVkitig==
  dependencies:
    aggregate-error "^3.0.0"
    p-cancelable "^2.0.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

package-json@^6.3.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/package-json/-/package-json-6.5.0.tgz"
  integrity sha512-k3bdm2n25tkyxcjSKzB5x8kfVxlMdgsbPr0GkZcwHsLpba6cBjqCt1KlcChKEvxHIcTB1FVMuwoijZ26xex5MQ==
  dependencies:
    got "^9.6.0"
    registry-auth-token "^4.0.0"
    registry-url "^5.0.0"
    semver "^6.2.0"

pako@~1.0.2:
  version "1.0.11"
  resolved "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==

papi@^0.29.0:
  version "0.29.1"
  resolved "https://registry.npmjs.org/papi/-/papi-0.29.1.tgz"
  integrity sha512-Y9ipSMfWuuVFO3zY9PlxOmEg+bQ7CeJ28sa9/a0veYNynLf9fwjR3+3fld5otEy7okUaEOUuCHVH62MyTmACXQ==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz"
  integrity sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw==

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.2.0.tgz"
  integrity sha512-jczvQbCUS7XmS7o+y1aEO9OBVFeZBQ1MDSEqmO7xSoPgOPoowY/SxLpZ6Vh97/8qHZOteiCKb7gkG9gA2ZUxJA==

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-2.0.0.tgz"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.2.2"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.2.2.tgz"
  integrity sha512-q0M/9eZHzmr0AulXyPwNfZjtwZ/RBZlbN3K3CErVrk50T2ASYI7Bye0EvekFY3IP1Nt2DHu0re+V2ZHIpMkuWg==

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-2.0.0.tgz"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/prepend-http/-/prepend-http-2.0.0.tgz"
  integrity sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc=

printj@~1.1.0, printj@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/printj/-/printj-1.1.2.tgz"
  integrity sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ==

process-nextick-args@~1.0.6:
  version "1.0.7"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz"
  integrity sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz"
  integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==

protobufjs@^6.8.6, protobufjs@^6.9.0:
  version "6.10.1"
  resolved "https://registry.npmjs.org/protobufjs/-/protobufjs-6.10.1.tgz"
  integrity sha512-pb8kTchL+1Ceg4lFd5XUpK8PdWacbvV5SK2ULH2ebrYtl4GjJmS24m6CKME67jzV53tbJxHlnNOSqQHbTsR9JQ==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.1"
    "@types/node" "^13.7.0"
    long "^4.0.0"

proxy-addr@~2.0.5:
  version "2.0.6"
  resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.6.tgz"
  integrity sha512-dh/frvCBVmSsDYzw6n926jv974gddhkFPfiN8hPOi30Wax25QZyZEGveluCgliBnqmuM+UJmBErbAUFIoDbjOw==
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.1"

proxyquire@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/proxyquire/-/proxyquire-2.1.3.tgz"
  integrity sha512-BQWfCqYM+QINd+yawJz23tbBM40VIGXOdDw3X344KcclI/gtBbdWF6SlQ4nK/bYhF9d27KYug9WzljHC6B9Ysg==
  dependencies:
    fill-keys "^1.0.2"
    module-not-found-error "^1.0.1"
    resolve "^1.11.1"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz"
  integrity sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ==

pstree.remy@^1.1.7:
  version "1.1.8"
  resolved "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz"
  integrity sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==

punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

pupa@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/pupa/-/pupa-2.0.1.tgz"
  integrity sha512-hEJH0s8PXLY/cdXh66tNEQGndDrIKNqNC5xmrysZy3i5C3oEoLna7YAOad+7u125+zH1HNXUmGEkrhb3c2VriA==
  dependencies:
    escape-goat "^2.0.0"

qs@^6.9.0:
  version "6.9.4"
  resolved "https://registry.npmjs.org/qs/-/qs-6.9.4.tgz"
  integrity sha512-A1kFqHekCTM7cz0udomYUoYNWjBebHm/5wzU/XqrBRBNWectVH0QIiN+NEcZ0Dte5hvzHwbr8+XQmguPhJ6WdQ==

qs@~6.5.2:
  version "6.5.2"
  resolved "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz"
  integrity sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA==

qs@6.7.0:
  version "6.7.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.7.0.tgz"
  integrity sha512-VCdBRNFTX1fyE7Nb6FYoURo/SPe62QCaAyzJvUjwRaIsc+NePBEniHlvxFmmX56+HZphIGtV0XeCirBtpDrTyQ==

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.4.0.tgz"
  integrity sha512-4Oz8DUIwdvoa5qMJelxipzi/iJIi40O5cGV1wNYp5hvZP8ZN0T+jiNkL0QepXs+EsQ9XJ8ipEDoiH70ySUJP3Q==
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc@^1.2.8:
  version "1.2.8"
  resolved "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz"
  integrity sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

read-excel-file@^4.0.5:
  version "4.0.6"
  resolved "https://registry.npmjs.org/read-excel-file/-/read-excel-file-4.0.6.tgz"
  integrity sha512-41aXtuZ9/D7cAYk5lJ9sNbH+8aKqiQNJMcmjlKSej65tYqUS2JztkgAuphjEV6D8OCl+mGvdTx/OFFGgiVslgQ==
  dependencies:
    jszip "3.1.5"
    unzipper "^0.9.15"
    xmldom "^0.1.27"
    xpath "0.0.27"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-2.0.0.tgz"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/read-pkg/-/read-pkg-2.0.0.tgz"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

readable-stream@^2.0.0:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.0.2:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.0.5:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.2.2:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.3.7:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz"
  integrity sha1-j5A0HmilPMySh4jaz80Rs265t44=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    string_decoder "~0.10.x"
    util-deprecate "~1.0.1"

readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@1.1.x:
  version "1.1.14"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz"
  integrity sha1-fPTFTvZI44EwhMY23SB54WbAgdk=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@~3.4.0:
  version "3.4.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.4.0.tgz"
  integrity sha512-0xe001vZBnJEK+uKcj8qOhyAKPzIT+gStxWr3LCB0DwcXR5NZJ3IaC+yGnHCYzB/S7ov3m3EEbZI2zeNvX+hGQ==
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redis-commands@^1.2.0, redis-commands@^1.3.1:
  version "1.6.0"
  resolved "https://registry.npmjs.org/redis-commands/-/redis-commands-1.6.0.tgz"
  integrity sha512-2jnZ0IkjZxvguITjFTrGiLyzQZcTvaw8DAaCXxZq/dsHXz7KfMQ3OUJy7Tz9vnRtZRVz6VRCPDvruvU8Ts44wQ==

redis-commands@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/redis-commands/-/redis-commands-1.7.0.tgz"
  integrity sha512-nJWqw3bTFy21hX/CPKHth6sfhZbdiHP6bTawSgQBlKOVRG7EZkfHbbHwQJnrE4vsQf0CMNE+3gJ4Fmm16vdVlQ==

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/redis-errors/-/redis-errors-1.2.0.tgz"
  integrity sha512-1qny3OExCf0UvUV/5wpYKf2YwPcOqXzkwKKSmKHiE6ZMQs5heeE/c8eXK+PNllPvmjgAbfnsbpkGZWy8cBpn9w==

redis-parser@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/redis-parser/-/redis-parser-2.6.0.tgz"
  integrity sha1-Uu0J2srBCPGmMcB+m2mUHnoZUEs=

redis-parser@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/redis-parser/-/redis-parser-3.0.0.tgz"
  integrity sha512-DJnGAeenTdpMEH6uAJRK/uiyEIH9WVsUmoLwzudwGJUwZPp80PDBWPHXSAGNPwNvIXAbe7MSUB1zQFugFml66A==
  dependencies:
    redis-errors "^1.0.0"

redis@^2.8.0:
  version "2.8.0"
  resolved "https://registry.npmjs.org/redis/-/redis-2.8.0.tgz"
  integrity sha512-M1OkonEQwtRmZv4tEWF2VgpG0JWJ8Fv1PhlgT5+B+uNq2cA3Rt1Yt/ryoR+vQNOQcIEgdCdfH0jr3bDpihAw1A==
  dependencies:
    double-ended-queue "^2.1.0-0"
    redis-commands "^1.2.0"
    redis-parser "^2.6.0"

redis@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/redis/-/redis-3.1.2.tgz"
  integrity sha512-grn5KoZLr/qrRQVwoSkmzdbw6pwF+/rwODtrOr6vuBRiR/f3rjSTGupbF90Zpqm2oenix8Do6RV7pYEkGwlKkw==
  dependencies:
    denque "^1.5.0"
    redis-commands "^1.7.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"

referrer-policy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/referrer-policy/-/referrer-policy-1.2.0.tgz"
  integrity sha512-LgQJIuS6nAy1Jd88DCQRemyE3mS+ispwlqMk3b0yjZ257fI1v9c+/p6SD5gP5FGyXUIgrNOAfmyioHwZtYv2VA==

reflect-metadata@^0.1.12, reflect-metadata@^0.1.13:
  version "0.1.13"
  resolved "https://registry.npmjs.org/reflect-metadata/-/reflect-metadata-0.1.13.tgz"
  integrity sha512-Ts1Y/anZELhSsjMcU605fU9RE4Oi3p5ORujwbIKXfWa+0Zxs510Qrmrce5/Jowq3cHSZSJqBjypxmHarc+vEWg==

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/regexpp/-/regexpp-2.0.1.tgz"
  integrity sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==

registry-auth-token@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/registry-auth-token/-/registry-auth-token-4.1.1.tgz"
  integrity sha512-9bKS7nTl9+/A1s7tnPeGrUpRcVY+LUh7bfFgzpndALdPfXQBfQV77rQVtqgUV3ti4vc/Ik81Ex8UJDWDQ12zQA==
  dependencies:
    rc "^1.2.8"

registry-url@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/registry-url/-/registry-url-5.1.0.tgz"
  integrity sha512-8acYXXTI0AkQv6RAOjE3vOaIXZkT9wo4LOFbBKYQEEnnMNBpKqdUrI6S4NT0KPIo/WVvJ5tE/X5LF/TQUf0ekw==
  dependencies:
    rc "^1.2.8"

request@^2.88.0, request@^2.88.2:
  version "2.88.2"
  resolved "https://registry.npmjs.org/request/-/request-2.88.2.tgz"
  integrity sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz"
  integrity sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==

requizzle@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npmjs.org/requizzle/-/requizzle-0.2.3.tgz"
  integrity sha512-YanoyJjykPxGHii0fZP0uUPEXpvqfBDxWV7s6GKAiiOsiqhX6vHNyW3Qzdmqp/iq/ExbhaGbVrjB4ruEVSM4GQ==
  dependencies:
    lodash "^4.17.14"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.11.1, resolve@^1.13.1, resolve@^1.17.0:
  version "1.17.0"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.17.0.tgz"
  integrity sha512-ic+7JYiV8Vi2yzQGFWOkiZD5Z9z7O2Zhm9XMaTxdJExKasieFCr+yXZ/WmXsckHiKl12ar0y6XiXDx3m4RHn1w==
  dependencies:
    path-parse "^1.0.6"

responselike@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/responselike/-/responselike-1.0.2.tgz"
  integrity sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec=
  dependencies:
    lowercase-keys "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

rfdc@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/rfdc/-/rfdc-1.1.4.tgz"
  integrity sha512-5C9HXdzK8EAqN7JDif30jqsBzavB7wLpaubisuQIGHWf2gUXSpzy6ArX/+Da8RjFpagWsCn+pIgxTMAmKw9Zug==

rimraf@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rimraf@~2.4.0:
  version "2.4.5"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.4.5.tgz"
  integrity sha512-J5xnxTyqaiw06JjMftq7L9ouA448dw/E7dKghkP9WpKNuwmARNNg+Gk8/u5ryb9N/Yo2+z3MCwuqFK/+qPOPfQ==
  dependencies:
    glob "^6.0.1"

rimraf@2:
  version "2.7.1"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz"
  integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz"
  integrity sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==
  dependencies:
    glob "^7.1.3"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz"
  integrity sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==

rxjs@^6.0.0, rxjs@^6.5.4, rxjs@^6.6.0:
  version "6.6.0"
  resolved "https://registry.npmjs.org/rxjs/-/rxjs-6.6.0.tgz"
  integrity sha512-3HMA8z/Oz61DUHe+SdOiQyzIf4tOx5oQHmMir7IZEu6TMqCLHT4LRcmNaUS0NwOz8VLvmmBduMsoaUvMaIiqzg==
  dependencies:
    tslib "^1.9.0"

safe-buffer@^5.0.1, safe-buffer@^5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1, safe-buffer@5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-json-stringify@~1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/safe-json-stringify/-/safe-json-stringify-1.2.0.tgz"
  integrity sha512-gH8eh2nZudPQO6TytOvbxnuhYBOvDBBLW52tz5q6X58lJcd/tkmqFR+5Z9adS8aJtURSXWThWy/xJtJwixErvg==

safer-buffer@^2.0.2, safer-buffer@^2.1.0, "safer-buffer@>= 2.1.2 < 3", safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sax@>=0.6.0, sax@1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz"
  integrity sha1-e45lYZCyKOgaZq6nSEgNgozS03o=

saxes@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz"
  integrity sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw==
  dependencies:
    xmlchars "^2.2.0"

semver-diff@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/semver-diff/-/semver-diff-3.1.1.tgz"
  integrity sha512-GX0Ix/CJcHyB8c4ykpHGIAvLyOwOobtM/8d+TQkAd81/bEjgPHrfba41Vpesr7jX/t8Uh+R3EX9eAS5be+jQYg==
  dependencies:
    semver "^6.3.0"

semver@^5.5.0:
  version "5.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==

semver@^5.6.0, semver@^5.7.1, "semver@2 || 3 || 4 || 5":
  version "5.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==

semver@^6.0.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^6.1.2:
  version "6.3.0"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^6.2.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^7.3.8:
  version "7.5.4"
  resolved "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

send@0.17.1:
  version "0.17.1"
  resolved "https://registry.npmjs.org/send/-/send-0.17.1.tgz"
  integrity sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg==
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.14.1.tgz"
  integrity sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-immediate-shim@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz"
  integrity sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E=

setimmediate@~1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.1.tgz"
  integrity sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw==

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shelljs@^0.8.2:
  version "0.8.4"
  resolved "https://registry.npmjs.org/shelljs/-/shelljs-0.8.4.tgz"
  integrity sha512-7gk3UZ9kOfPLIAbslLzyWeGiEqx9e3rxwZM0KE6EL8GlGwjym9Mrlx5/p33bWTu9YG6vcS4MBxYZDHYr5lr8BQ==
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

sigmund@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz"
  integrity sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA=

signal-exit@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.3.tgz"
  integrity sha512-VUJ49FC8U1OxwZLxIbTTrDvLnf/6TDgxZcK8wxR8zs13xpx7xbG60ndBlhNrFi2EMuFRoeDoJO7wthSLq42EjA==

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/slice-ansi/-/slice-ansi-2.1.0.tgz"
  integrity sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

source-map-support@^0.5.17:
  version "0.5.19"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.19.tgz"
  integrity sha512-Wonm7zOCIJzBGQdB+thsPar0kYuCIzYvxZwlBa87yi/Mdjv7Tip2cyVbLj5o0cFPN4EVkuTwb3GDDyUx2DGnGw==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.1.tgz"
  integrity sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  integrity sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz"
  integrity sha512-J+FWzZoynJEXGphVIS+XEh3kFSjZX/1i9gFBaWQcB+/tmpe2qUsSBABpcxqxnAxFdiUFEgAX1bjYGQvIZmoz9Q==

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

ssf@~0.10.3:
  version "0.10.3"
  resolved "https://registry.npmjs.org/ssf/-/ssf-0.10.3.tgz"
  integrity sha512-pRuUdW0WwyB2doSqqjWyzwCD6PkfxpHAHdZp39K3dp/Hq7f+xfMwNAWIi16DyrRg4gg9c/RvLYkJTSawTPTm1w==
  dependencies:
    frac "~1.1.2"

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz"
  integrity sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg==
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

stack-trace@0.0.x:
  version "0.0.10"
  resolved "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz"
  integrity sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=

"statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

streamroller@^2.2.4:
  version "2.2.4"
  resolved "https://registry.npmjs.org/streamroller/-/streamroller-2.2.4.tgz"
  integrity sha512-OG79qm3AujAM9ImoqgWEY1xG4HX+Lw+yY6qZj9R1K2mhF5bEmQ849wvrb+4vt4jLMLzwXttJlQbOdPOQVRv7DQ==
  dependencies:
    date-format "^2.1.0"
    debug "^4.1.1"
    fs-extra "^8.1.0"

streamsearch@0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.2.tgz"
  integrity sha1-gIudDlb8Jz2Am6VzOOkpkZoanxo=

string_decoder@^1.1.1, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string-width@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz"
  integrity sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz"
  integrity sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.0.0, string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.0.tgz"
  integrity sha512-zUz5JD+tgqtuDjMhwIg5uFVV3dtqZ9yQJlZVfq4I01/K5Paj5UHj7VyrQOJvzawSVlKpObApbfD0Ed6yJc+1eg==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.trimend@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.1.tgz"
  integrity sha512-LRPxFUaTtpqYsTeNKaFOw3R4bxIzWOnbQ837QfBylo8jIxtcbK/A/sMV7Q+OAV/vWo+7s25pOE10KYSjaSO06g==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trimstart@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.1.tgz"
  integrity sha512-XxZn+QpvrBI1FOcg6dIpxUPgWCPuNXvMD72aaRaUQv1eD4e/Qy8i/hFTe0BUmD60p/QA6bh1avmuPTfNjqVWRw==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz"
  integrity sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.0.tgz"
  integrity sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w==
  dependencies:
    ansi-regex "^5.0.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-json-comments@^3.0.1, strip-json-comments@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.0.tgz"
  integrity sha512-e6/d0eBu7gHtdCqFt0xJr642LdToM5/cN4Qb9DbHjVx1CP5RyeM+zH7pbecEmDv/lBqb0QH+6Uqq75rxFPkM0w==

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

supports-color@^5.3.0, supports-color@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.1.0.tgz"
  integrity sha512-oRSIpR8pxT1Wr2FquTNnGet79b3BWljqOuoW/h4oBhxJ/HUbX5nX6JSruTkvXDCFMwDPvsaTTbvMLKZWSy0R5g==
  dependencies:
    has-flag "^4.0.0"

swagger-fluent@^5.0.1:
  version "5.0.3"
  resolved "https://registry.npmjs.org/swagger-fluent/-/swagger-fluent-5.0.3.tgz"
  integrity sha512-i43ADMtPi7dxAN75Lw50SlncMB31FgaVwXqKioR8SWs+Yon2RbiLU1J1PGMXA4N8cSt9Vz5RHzaoKjz/+iW88g==
  dependencies:
    deepmerge "^4.2.2"
    is-plain-object "^3.0.0"
    request "^2.88.0"

swagger-ui-dist@^3.18.1:
  version "3.28.0"
  resolved "https://registry.npmjs.org/swagger-ui-dist/-/swagger-ui-dist-3.28.0.tgz"
  integrity sha512-aPkfTzPv9djSiZI1NUkWr5HynCUsH+jaJ0WSx+/t19wq7MMGg9clHm9nGoIpAtqml1G51ofI+I75Ym72pukzFg==

swagger-ui-express@^4.1.3:
  version "4.1.4"
  resolved "https://registry.npmjs.org/swagger-ui-express/-/swagger-ui-express-4.1.4.tgz"
  integrity sha512-Ea96ecpC+Iq9GUqkeD/LFR32xSs8gYqmTW1gXCuKg81c26WV6ZC2FsBSPVExQP6WkyUuz5HEiR0sEv/HCC343g==
  dependencies:
    swagger-ui-dist "^3.18.1"

table@^5.2.3:
  version "5.4.6"
  resolved "https://registry.npmjs.org/table/-/table-5.4.6.tgz"
  integrity sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

taffydb@2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/taffydb/-/taffydb-2.6.2.tgz"
  integrity sha1-fLy2S1oUG2ou/CxdLGe04VCyomg=

tar-stream@^2.1.2:
  version "2.1.3"
  resolved "https://registry.npmjs.org/tar-stream/-/tar-stream-2.1.3.tgz"
  integrity sha512-Z9yri56Dih8IaK8gncVPx4Wqt86NDmQTSh49XLZgjWpGZL9GK9HKParS2scqHCC4w6X9Gh2jwaU45V47XTKwVA==
  dependencies:
    bl "^4.0.1"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

term-size@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/term-size/-/term-size-2.2.0.tgz"
  integrity sha512-a6sumDlzyHVJWb8+YofY4TW112G6p2FCPEAFk+59gIYHv3XHRhm9ltVQ9kli4hNWeQBwSpe8cRN25x0ROunMOw==

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

timers-ext@^0.1.5, timers-ext@^0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/timers-ext/-/timers-ext-0.1.7.tgz"
  integrity sha512-b85NUNzTSdodShTIbky6ZF02e8STtVVfD+fu4aXXShEELpozH+bCpJLYMPZbsABN2wDH7fJpqIoXxJpzbf0NqQ==
  dependencies:
    es5-ext "~0.10.46"
    next-tick "1"

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
  integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
  dependencies:
    os-tmpdir "~1.0.2"

tmp@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmjs.org/tmp/-/tmp-0.2.1.tgz"
  integrity sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ==
  dependencies:
    rimraf "^3.0.0"

to-readable-stream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/to-readable-stream/-/to-readable-stream-1.0.0.tgz"
  integrity sha512-Iq25XBt6zD5npPhlLVXGFN3/gyR2/qODcKNNyTMd4vbm39HUaOiAM4PMq0eMVC/Tkxz+Zjdsc55g9yyz+Yq00Q==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.0.tgz"
  integrity sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw==

touch@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/touch/-/touch-3.1.0.tgz"
  integrity sha512-WBx8Uy5TLtOSRtIq+M03/sKDrXCLHxwDcquSP2c43Le03/9serjQBIztjRz6FkJez9D/hleyAXTBGLwwZUw9lA==
  dependencies:
    nopt "~1.0.10"

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz"
  integrity sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

"traverse@>=0.3.0 <0.4":
  version "0.3.9"
  resolved "https://registry.npmjs.org/traverse/-/traverse-0.3.9.tgz"
  integrity sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk=

triple-beam@^1.2.0, triple-beam@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/triple-beam/-/triple-beam-1.3.0.tgz"
  integrity sha512-XrHUvV5HpdLmIj4uVMxHggLbFSZYIn7HEWsqePZcI50pco+MPqJ50wMGY794X7AOOhxOBAjbkqfAbEe/QMp2Lw==

ts-node@^8.6.2:
  version "8.10.2"
  resolved "https://registry.npmjs.org/ts-node/-/ts-node-8.10.2.tgz"
  integrity sha512-ISJJGgkIpDdBhWVu3jufsWpK3Rzo7bdiIXJjQc0ynKxVOVcg2oIrf2H2cejminGrptVc6q6/uynAHNCuWGbpVA==
  dependencies:
    arg "^4.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    source-map-support "^0.5.17"
    yn "3.1.1"

tsc@^1.20150623.0:
  version "1.20150623.0"
  resolved "https://registry.npmjs.org/tsc/-/tsc-1.20150623.0.tgz"
  integrity sha1-Trw8d04WkUjLx2inNCUz8ILHpuU=

tsconfig-paths@^3.9.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.9.0.tgz"
  integrity sha512-dRcuzokWhajtZWkQsDVKbWyY+jgcLC5sqJhg2PSgf4ZkH2aHPvaOY8YWGhmjb68b5qqTfasSsDO9k7RUiEmZAw==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@^1.9.0, tslib@^1.9.3:
  version "1.13.0"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.13.0.tgz"
  integrity sha512-i/6DQjL8Xf3be4K/E6Wgpekn5Qasl1usyw++dAA35Ue5orEn65VIxOA+YvNNl9HV3qv70T7CNwjODHZrLwvd1Q==

tslib@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.0.0.tgz"
  integrity sha512-lTqkx847PI7xEDYJntxZH89L2/aXInsyF2luSafe/+0fHOMjlBNXdH6th7f70qxLDhul7KZK0zC8V5ZIyHl0/g==

tsoa@^2.5.13:
  version "2.5.14"
  resolved "https://registry.npmjs.org/tsoa/-/tsoa-2.5.14.tgz"
  integrity sha512-wBqUCgsWaypR3J5oWsp7AnOHoD4Yfa6g9CnZ+5ImHdXg3VGjBj8BKCO+k3u5gGj5xf3/rjpuAAihAAWV0wHBhw==
  dependencies:
    fs-extra "^8.1.0"
    handlebars "^4.5.3"
    merge "^1.2.1"
    minimatch "^3.0.4"
    moment "^2.24.0"
    typescript "^3.5.3"
    typescript-formatter "^7.2.2"
    validator "^11.1.0"
    yamljs "^0.3.0"
    yargs "^14.0.0"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.11.0.tgz"
  integrity sha512-OdjXJxnCN1AvyLSzeKIgXTXxV+99ZuXl3Hpo9XpJAv9MBcHrrJOQ5kV7ypXOuQie+AmWG25hLbiKdwYTifzcfQ==

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz"
  integrity sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==

type-is@^1.6.4, type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/type/-/type-1.2.0.tgz"
  integrity sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg==

type@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/type/-/type-2.1.0.tgz"
  integrity sha512-G9absDWvhAWCV2gmF1zKud3OyC61nZDwWvBL2DApaVFogI07CprggiQAOOjvp2NRjYWFzPyu7vwtDrQFq8jeSA==

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  integrity sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript-formatter@^7.2.2:
  version "7.2.2"
  resolved "https://registry.npmjs.org/typescript-formatter/-/typescript-formatter-7.2.2.tgz"
  integrity sha512-V7vfI9XArVhriOTYHPzMU2WUnm5IMdu9X/CPxs8mIMGxmTBFpDABlbkBka64PZJ9/xgQeRpK8KzzAG4MPzxBDQ==
  dependencies:
    commandpost "^1.0.0"
    editorconfig "^0.15.0"

"typescript@^2.1.6 || >=2.7.0-dev || >=2.8.0-dev || >=2.9.0-dev || >=3.0.0-dev", typescript@^3.5.3, typescript@^3.7.5, typescript@>=2.7:
  version "3.9.6"
  resolved "https://registry.npmjs.org/typescript/-/typescript-3.9.6.tgz"
  integrity sha512-Pspx3oKAPJtjNwE92YS05HQoY7z2SFyOpHo9MqJor3BXAGNaPUs83CuVp9VISFkSjyRfiTpmKuAYGJB7S7hOxw==

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npmjs.org/uc.micro/-/uc.micro-1.0.6.tgz"
  integrity sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA==

uglify-js@^3.1.4:
  version "3.10.0"
  resolved "https://registry.npmjs.org/uglify-js/-/uglify-js-3.10.0.tgz"
  integrity sha512-Esj5HG5WAyrLIdYU74Z3JdG2PxdIusvj6IWHMtlyESxc7kcDz7zYlYjpnSokn1UbpV0d/QX9fan7gkCNd/9BQA==

undefsafe@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.3.tgz"
  integrity sha512-nrXZwwXrD/T/JXeygJqdCO6NZZ1L66HrxM/Z7mIq2oPanoN0F1nLx3lwJMu6AwJY69hdixaFQOuoYsMjE5/C2A==
  dependencies:
    debug "^2.2.0"

underscore@^1.9.1, underscore@~1.10.2:
  version "1.10.2"
  resolved "https://registry.npmjs.org/underscore/-/underscore-1.10.2.tgz"
  integrity sha512-N4P+Q/BuyuEKFJ43B9gYuOj4TQUHXX+j2FqguVOpjkssLUUrnJofCcBccJSCoeturDoZU6GorDTHSvUDlSQbTg==

unique-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz"
  integrity sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unzipper@^0.10.11:
  version "0.10.11"
  resolved "https://registry.npmjs.org/unzipper/-/unzipper-0.10.11.tgz"
  integrity sha512-+BrAq2oFqWod5IESRjL3S8baohbevGcVA+teAIOYWM3pDVdseogqbzhhvvmiyQrUNKFUnDMtELW3X8ykbyDCJw==
  dependencies:
    big-integer "^1.6.17"
    binary "~0.3.0"
    bluebird "~3.4.1"
    buffer-indexof-polyfill "~1.0.0"
    duplexer2 "~0.1.4"
    fstream "^1.0.12"
    graceful-fs "^4.2.2"
    listenercount "~1.0.1"
    readable-stream "~2.3.6"
    setimmediate "~1.0.4"

unzipper@^0.9.15:
  version "0.9.15"
  resolved "https://registry.npmjs.org/unzipper/-/unzipper-0.9.15.tgz"
  integrity sha512-2aaUvO4RAeHDvOCuEtth7jrHFaCKTSXPqUkXwADaLBzGbgZGzUDccoEdJ5lW+3RmfpOZYNx0Rw6F6PUzM6caIA==
  dependencies:
    big-integer "^1.6.17"
    binary "~0.3.0"
    bluebird "~3.4.1"
    buffer-indexof-polyfill "~1.0.0"
    duplexer2 "~0.1.4"
    fstream "^1.0.12"
    listenercount "~1.0.1"
    readable-stream "~2.3.6"
    setimmediate "~1.0.4"

update-notifier@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/update-notifier/-/update-notifier-4.1.0.tgz"
  integrity sha512-w3doE1qtI0/ZmgeoDoARmI5fjDoT93IfKgEGqm26dGUOh8oNpaSTsGNdYRN/SjOuo10jcJGwkEL3mroKzktkew==
  dependencies:
    boxen "^4.2.0"
    chalk "^3.0.0"
    configstore "^5.0.1"
    has-yarn "^2.1.0"
    import-lazy "^2.1.0"
    is-ci "^2.0.0"
    is-installed-globally "^0.3.1"
    is-npm "^4.0.0"
    is-yarn-global "^0.3.0"
    latest-version "^5.0.0"
    pupa "^2.0.1"
    semver-diff "^3.1.1"
    xdg-basedir "^4.0.0"

uri-js@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz"
  integrity sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==
  dependencies:
    punycode "^2.1.0"

uri-params@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/uri-params/-/uri-params-0.1.3.tgz"
  integrity sha1-0IReC8MbFi6KqGfHeML2w0O6EyA=

url-join@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz"
  integrity sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==

url-parse-lax@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/url-parse-lax/-/url-parse-lax-3.0.0.tgz"
  integrity sha1-FrXK/Afb42dsGxmZF3gj1lA6yww=
  dependencies:
    prepend-http "^2.0.0"

url@0.10.3:
  version "0.10.3"
  resolved "https://registry.npmjs.org/url/-/url-0.10.3.tgz"
  integrity sha1-Ah5NnHcF8hu/N9A861h2dAJ3TGQ=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2, uuid@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
  integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==

uuid@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/uuid/-/uuid-7.0.3.tgz"
  integrity sha512-DPSke0pXhTZgoF/d+WSt2QaKMCFSfx7QegxEWT+JOuHF5aWrKEn0G+ztjuJg/gG8/ItK+rbPCD/yNv8yyih6Cg==

uuid@3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz"
  integrity sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA==

uuid@8.3.0:
  version "8.3.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.0.tgz"
  integrity sha512-fX6Z5o4m6XsXBdli9g7DtWgAx+osMsRRZFKma1mIUsLCz6vRvv+pz5VNbyu9UEDzpMWulZfvpgb/cmDXVulYFQ==

v8-compile-cache@^2.0.3:
  version "2.1.1"
  resolved "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.1.1.tgz"
  integrity sha512-8OQ9CL+VWyt3JStj7HX7/ciTL2V3Rl1Wf5OL+SNTm0yK1KvtReVulksyeRnCANHHuUxHlQig+JJDlUhBt1NQDQ==

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validator@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/validator/-/validator-11.1.0.tgz"
  integrity sha512-qiQ5ktdO7CD6C/5/mYV4jku/7qnqzjrxb3C/Q5wR3vGGinHTgJZN/TdFT3ZX4vXhX2R1PXx42fB1cn5W+uJ4lg==

vary@^1, vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

widest-line@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/widest-line/-/widest-line-3.1.0.tgz"
  integrity sha512-NsmoXalsWVDMGupxZ5R08ka9flZjjiLvHVAWYOKtiKM8ujtZWr9cRffak+uSE48+Ob8ObalXpwyeUiyDD6QFgg==
  dependencies:
    string-width "^4.0.0"

winston-compat@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npmjs.org/winston-compat/-/winston-compat-0.1.5.tgz"
  integrity sha512-EPvPcHT604AV3Ji6d3+vX8ENKIml9VSxMRnPQ+cuK/FX6f3hvPP2hxyoeeCOCFvDrJEujalfcKWlWPvAnFyS9g==
  dependencies:
    cycle "~1.0.3"
    logform "^1.6.0"
    triple-beam "^1.2.0"

winston-daily-rotate-file@^3.8.0:
  version "3.10.0"
  resolved "https://registry.npmjs.org/winston-daily-rotate-file/-/winston-daily-rotate-file-3.10.0.tgz"
  integrity sha512-KO8CfbI2CvdR3PaFApEH02GPXiwJ+vbkF1mCkTlvRIoXFI8EFlf1ACcuaahXTEiDEKCii6cNe95gsL4ZkbnphA==
  dependencies:
    file-stream-rotator "^0.4.1"
    object-hash "^1.3.0"
    semver "^6.2.0"
    triple-beam "^1.3.0"
    winston-compat "^0.1.4"
    winston-transport "^4.2.0"

winston-transport@^4.2.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/winston-transport/-/winston-transport-4.4.0.tgz"
  integrity sha512-Lc7/p3GtqtqPBYYtS6KCN3c77/2QCev51DvcJKbkFPQNoj1sinkGwLGFDxkXY9J6p9+EPnYs+D90uwbnaiURTw==
  dependencies:
    readable-stream "^2.3.7"
    triple-beam "^1.2.0"

"winston@^2 || ^3", winston@^2.4.2:
  version "2.4.5"
  resolved "https://registry.npmjs.org/winston/-/winston-2.4.5.tgz"
  integrity sha512-TWoamHt5yYvsMarGlGEQE59SbJHqGsZV8/lwC+iCcGeAe0vUaOh+Lv6SYM17ouzC/a/LB1/hz/7sxFBtlu1l4A==
  dependencies:
    async "~1.0.0"
    colors "1.0.x"
    cycle "1.0.x"
    eyes "0.1.x"
    isstream "0.1.x"
    stack-trace "0.0.x"

wmf@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wmf/-/wmf-1.0.2.tgz"
  integrity sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
  integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-5.1.0.tgz"
  integrity sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  integrity sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

write@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/write/-/write-1.0.3.tgz"
  integrity sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==
  dependencies:
    mkdirp "^0.5.1"

ws@*, ws@^7.2.3:
  version "7.3.1"
  resolved "https://registry.npmjs.org/ws/-/ws-7.3.1.tgz"
  integrity sha512-D3RuNkynyHmEJIpD2qrgVkc9DQ23OrN/moAwZX4L8DfvszsJxpjQuUq3LMx6HoYji9fbIOBY18XWBsAux1ZZUA==

ws@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ws/-/ws-6.2.1.tgz"
  integrity sha512-GIyAXC2cB7LjvpgMt9EKS2ldqr0MTrORaleiOno6TweZ6r3TKtoFQWay/2PceJ3RuBasOHzXNn5Lrw1X0bEjqA==
  dependencies:
    async-limiter "~1.0.0"

x-xss-protection@1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/x-xss-protection/-/x-xss-protection-1.3.0.tgz"
  integrity sha512-kpyBI9TlVipZO4diReZMAHWtS0MMa/7Kgx8hwG/EuZLiA6sg4Ah/4TRdASHhRRN3boobzcYgFRUFSgHRge6Qhg==

xdg-basedir@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-4.0.0.tgz"
  integrity sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==

xlsx@^0.15.5:
  version "0.15.6"
  resolved "https://registry.npmjs.org/xlsx/-/xlsx-0.15.6.tgz"
  integrity sha512-7vD9eutyLs65iDjNFimVN+gk/oDkfkCgpQUjdE82QgzJCrBHC4bGPH7fzKVyy0UPp3gyFVQTQEFJaWaAvZCShQ==
  dependencies:
    adler-32 "~1.2.0"
    cfb "^1.1.4"
    codepage "~1.14.0"
    commander "~2.17.1"
    crc-32 "~1.2.0"
    exit-on-epipe "~1.0.1"
    ssf "~0.10.3"
    wmf "~1.0.1"

xml2js@0.4.19:
  version "0.4.19"
  resolved "https://registry.npmjs.org/xml2js/-/xml2js-0.4.19.tgz"
  integrity sha512-esZnJZJOiJR9wWKMyuvSE1y6Dq5LCuJanqhxslH2bxM6duahNZ+HMpCLhBQGZkbX6xRf8x1Y2eJlgt2q3qo49Q==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~9.0.1"

xmlbuilder@~9.0.1:
  version "9.0.7"
  resolved "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-9.0.7.tgz"
  integrity sha1-Ey7mPS7FVlxVfiD0wi35rKaGsQ0=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
  integrity sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==

xmlcreate@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/xmlcreate/-/xmlcreate-2.0.3.tgz"
  integrity sha512-HgS+X6zAztGa9zIK3Y3LXuJes33Lz9x+YyTxgrkIdabu2vqcGOWwdfCpf1hWLRrd553wd4QCDf6BBO6FfdsRiQ==

xmldom@^0.1.27:
  version "0.1.31"
  resolved "https://registry.npmjs.org/xmldom/-/xmldom-0.1.31.tgz"
  integrity sha512-yS2uJflVQs6n+CyjHoaBmVSqIDevTAWrzMmjG1Gc7h1qQ7uVozNhEPJAwZXWyGQ/Gafo3fCwrcaokezLPupVyQ==

xpath@0.0.27:
  version "0.0.27"
  resolved "https://registry.npmjs.org/xpath/-/xpath-0.0.27.tgz"
  integrity sha512-fg03WRxtkCV6ohClePNAECYsmpKKTv5L8y/X3Dn1hQrec3POx2jHZ/0P2qQ6HvsrU1BmeqXcof3NGGueG6LxwQ==

xtend@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

y18n@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/y18n/-/y18n-4.0.0.tgz"
  integrity sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w==

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yamljs@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/yamljs/-/yamljs-0.3.0.tgz"
  integrity sha512-C/FsVVhht4iPQYXOInoxUM/1ELSf9EsgKH34FofQOp6hwCPrW4vG4w5++TED3xRUo8gD7l0P1J1dLlDYzODsTQ==
  dependencies:
    argparse "^1.0.7"
    glob "^7.0.5"

yargs-parser@^15.0.1:
  version "15.0.1"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-15.0.1.tgz"
  integrity sha512-0OAMV2mAZQrs3FkNpDQcBk1x5HXb8X4twADss4S0Iuk+2dGnLOE/fRHrsYm542GduMveyA77OF4wrNJuanRCWw==
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  integrity sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^14.0.0:
  version "14.2.3"
  resolved "https://registry.npmjs.org/yargs/-/yargs-14.2.3.tgz"
  integrity sha512-ZbotRWhF+lkjijC/VhmOT9wSgyBQ7+zr13+YLkhfsSiTriYsMzkTUFP18pFhWwBeMa5gUc1MzbhrO6/VB7c9Xg==
  dependencies:
    cliui "^5.0.0"
    decamelize "^1.2.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^15.0.1"

yargs@^15.3.1:
  version "15.4.1"
  resolved "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  integrity sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yn@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  integrity sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==

zip-stream@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/zip-stream/-/zip-stream-3.0.1.tgz"
  integrity sha512-r+JdDipt93ttDjsOVPU5zaq5bAyY+3H19bDrThkvuVxC0xMQzU1PJcS6D+KrP3u96gH9XLomcHPb+2skoDjulQ==
  dependencies:
    archiver-utils "^2.1.0"
    compress-commons "^3.0.0"
    readable-stream "^3.6.0"
