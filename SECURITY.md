## Política de Updates de Segurança
Caso alguma vulnerabilidade seja encontrada no sistema, reportar à equipe de Segurança da Informação através do e-mail: <EMAIL>

## Considerações para configurações de segurança
* Utilizar HTTPS sempre que possível
* Alterar credenciais padrão
* Não gravar credenciais de acesso hard-coded no código do projeto
* Realizar filtros em inputs de usuários e validações em objetos remotos

## Portas expostas 
(Informar portas e serviços relacionados à aplicação)

| Porta | Cliente / Host / Processos | Tipo de Servidor |
|--------------|--------------|------------------------|--------|
| Ex: 8080 | Ex: `ui.port` | Ex: Client Web  | Ex: UI |

## Logs
(Informar local onde serão armazenados os access logs da aplicação)

## Autenticação
(Como é feita a autenticação dos serviços e/ou logins)

## Diagrama de Fluxo de Dados

(DFD da aplicação)