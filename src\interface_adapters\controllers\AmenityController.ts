import { Request, Response } from 'express';
//import {ConfigOracleDB} from '../../frameworks_drivers/database/configOracleDB'
//import consultKey from '../../frameworks_drivers/middlewares/consulConsult'
import BrokerVhc from '../../application_bussines_rules/brokers/brokerVhc'
import Util from '../../enterprise_bussines_rules/utils/util'
/**
 * @class AmenityController
 * @classdesc Controller com as funções que retornam informações das amenidades
 * <AUTHOR>
 */
export default class AmenityController {    

    //private configOracleDB = new ConfigOracleDB();
    private util = new Util();

    /**
     * @method getAmenities
     * @param  {req} - Request
     * @param  {res} - Response
     * @returns {retorno} - retorno
     */
    public getAmenities  = async (req: Request, res: Response) => {        
        const brokerVhc = new BrokerVhc();
        await brokerVhc.init();
        let credentials = await brokerVhc.getCredential(req);        
        let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom);
        let value = await this.util.convertAmenityResponse(await brokerVhc.amenityList("pt", apiToken));        
        let retorno = {status:"ok",  value: value};                
        res.status(200)
            .json(retorno)
    }
}
