import atob from 'atob';
import btoa from 'btoa';
import crypto, { HexBase64BinaryEncoding, Utf8AsciiBinaryEncoding } from 'crypto';

export class DataSecurity {

    private static algorithm = 'aes-128-cbc'

    private static genereteSecretKey(): string {
        const hex: string = crypto.randomBytes(60).toString('hex');

        return hex.substr(15, 16);

    }

    private static generateIV(): Buffer {
        return crypto.randomBytes(16);
    }

    public static encrypt(data: any, options: {
        inputCharset: Utf8AsciiBinaryEncoding,
        outputCharset: HexBase64BinaryEncoding,
        base64Encoding: boolean
    }) {

        if (data instanceof Object)
            data = JSON.stringify(data);

        const secretKey: string = this.genereteSecretKey();
        const iv: Buffer = this.generateIV();

        const cipher = crypto.createCipheriv(this.algorithm, Buffer.from(secretKey), iv);
        let encrypted = cipher.update(data, options.inputCharset, options.outputCharset);
        encrypted += cipher.final(options.outputCharset);

        if (options.base64Encoding)
            encrypted = btoa(encrypted);

        return `${secretKey}T3r$oS${iv.toString('hex')}.${encrypted}`;
    }

    public static decrypt(encrypt: any, options: {
        secretKey: Buffer,
        iv: Buffer,
        inputCharset: HexBase64BinaryEncoding,
        outputCharset: Utf8AsciiBinaryEncoding,
        base64Decoding: boolean
    }) {
        if(options.base64Decoding)
            encrypt = atob(encrypt);

        const decipher = crypto.createDecipheriv(this.algorithm, options.secretKey, options.iv);
        let decrypted = decipher.update(encrypt, options.inputCharset, options.outputCharset);
        decrypted += decipher.final(options.outputCharset);

        return JSON.parse(decrypted);
    }

}
