apiVersion: v1
kind: ConfigMap
metadata:
  name: corp-wl-vhs-bff
  namespace: corp-wl-vhs
data:
  ADDITIONAL_OPTS: " "
  PREFIX: corp-wl-vhs-bff
  VAULT_HOST: vault.prod.cvc.intra
  VAULT_SCHEME: http
  CONSUL_HOST: consul.prod.cvc.intra
  CONSUL_PORT: "8500"
  CONSUL_URL: consul-prod.services.cvc.com.br
  SECRET_JWT: '1234567890123456'
  BASE_URL_VHC: base_url_vhc
  BASE_URL_CREDENTIAL: base_url_credential
  VHC_X_API_KEY: vhc_x_api_key
  DECRYPT_KEY: decrypt_key
  ZONES: zones    
  BASE_URL_VAULT: base_url_vault
  X_VAULT_TOKEN: x_vault_token
  DB_CONNECTION: db_connection  
  BASE_URL_DROOLS: base_url_drools
  BASE_URL_VAULT_CREDENTIAL: base_url_vault_credential
  ACELERATOR_HOUSES_VIEW: acelerator_houses_view
  ACELERATOR_DAYS_COUNT: acelerator_days_count
  X_VAULT_CREDENTIAL_TOKEN: x_vault_credential_token
  BASE_URL_PAYMMENT: base_url_paymment
  MS_SHOPPING_CART_URL: "https://ms-shopping-cart.k8s-cvc.com.br/"
  NODE_ENV: prod
  LOG_HOST: logstash.services.cvc.com.br
  LOG_PORT: "12201"
