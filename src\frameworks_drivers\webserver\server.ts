import compression from 'compression'
import cors from 'cors'
import express from 'express'
import helmet from 'helmet'
import http from 'http'
import routes from './routes'

class Server {
    private server: express.Express;

    constructor() {
        this.server = express()

        this.server.use(express.json())
        this.server.use(express.urlencoded({ extended: false }))
        this.server.use(compression())
        this.server.use(helmet())

        this.server.use(cors())        

        this.server.use(routes)
    }

    public createHTTP() {
        return http.createServer(this.server);
    }
}

export default Server;
