import crypto from 'crypto'

export default class AesDecrypt{


    public decrypt(key:any, str:any){
        let bytes = key.split('').map(this.s);
        let keyBytes = new Uint8Array(bytes);
        var decipher = crypto.createDecipheriv("aes-128-ecb", keyBytes, Buffer.alloc(0));
        let buffer = this.decodeHex(str.split(''));
        let dec = Buffer.concat([decipher.update(buffer), decipher.final()]);                        
        let decrypt = dec.toString('utf8')
        return decrypt;
    }

    public s(x:any) {return x.charCodeAt(0);}

    public decodeHex(data:any){        
        let len = data.length;
        if ((len & 0x01) != 0) {
            throw new Error("Odd number of characters.");
        }

        let out:Uint8Array = new Uint8Array(len >> 1);        

        for (let i = 0, j = 0; j < len; i++) {
            let f = this.toDigit(data[j], j) << 4;
            j++;
            f = f | this.toDigit(data[j], j);
            j++;
            out[i] = (f & 0xFF);
        }

        return out;
    }

    public toDigit(ch:any,index:any){
        let digit =  parseInt(ch, 16)        
        if (digit == -1) {
            throw new Error("Illegal hexadecimal character " + ch + " at index " + index);
        }
        return digit;
    }    


}