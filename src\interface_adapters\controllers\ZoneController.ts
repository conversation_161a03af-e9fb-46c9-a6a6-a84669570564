import { Request, Response } from 'express';
import uuid from 'uuid';
import BrokerVhc from '../../application_bussines_rules/brokers/brokerVhc'
import Util from '../../enterprise_bussines_rules/utils/util'
/**
 * @class ZoneController
 * @classdesc Controller com as funções que retornam informações das zonas
 * <AUTHOR>
 */
export default class ZoneController {
    private util = new Util();

    /**
     * @method getZone
     * @param  {req} - Request
     * @param  {res} - Response
     * @returns {retorno} - retorno
     */
    public getZone = async (req: Request, res: Response) => {

        if (!req.headers['gtw-transaction-id']) req.headers['gtw-transaction-id'] = uuid();

        const transactioId = req.headers['gtw-transaction-id'];

        try {

            const brokerVhc = new BrokerVhc();

            await brokerVhc.init();

            let credentials = await brokerVhc.getCredential(req);

            let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom);
            let value = await this.util.convertZoneResponse(await brokerVhc.zoneList("pt", apiToken));
            let retorno = { status: "ok", value: value, transactioId };
            res.status(200)
                .json(retorno);

        }
        catch (err) {

            res.status(500)
                .json({ status: "error", value: err.message, transactioId });

        }
    }
}
