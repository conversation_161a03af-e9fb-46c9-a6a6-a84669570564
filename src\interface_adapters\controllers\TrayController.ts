import { Request, Response } from 'express';
import BrokerVhc from '../../application_bussines_rules/brokers/brokerVhc'
import Redis from '../../frameworks_drivers/middlewares/redis'
//import {LogstashService} from '../../frameworks_drivers/middlewares/logStash'
//import PropertyController from './PropertyController'
import moment from 'moment'
import Util from '../../enterprise_bussines_rules/utils/util'

export default class TrayController {


    private redis = new Redis();
    //private logger = new LogstashService();
    private util = new Util();
    //private propertyControler = new PropertyController();

    public send = async (req: Request, res: Response) => {
        try {
            console.log("Inicio send bandeija");
    
            let request = req.body;
            request.email = "<EMAIL>";
            req.params.keyRateToken = request.keyRateToken;
    
            let responseHasAvail = await this.hasAvail(req);
            if (responseHasAvail.status == "erro") {
                console.error("Erro na verificação de disponibilidade");
                return res.status(200).json(responseHasAvail);
            }
    
            let rateToken: any = await this.redis.getKey(request.keyRateToken);
            request.message = JSON.stringify(rateToken.rate);
            request.checkin = moment(rateToken.startDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss");
            request.checkout = moment(rateToken.endDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss");
            request.propertyID = rateToken.idPmsExternal;
            request.currencyWished = "PROPERTY_DEFAULT";
            request.language = "pt";
    
            if (!request.bookingID || request.bookingID.trim() === "") {
                request.bookingID = "PORTALOPERADORAS";
            }
    
            delete request.keyRateToken;
            const brokerVhc = new BrokerVhc();
            await brokerVhc.init();
    
            let credentials = await brokerVhc.getCredential(req);
            let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom);
    
            let responseStart = await brokerVhc.startBooking(apiToken, request);
            console.log("cURL enviado para o parceiro:", JSON.stringify(request, null, 2));
            
            let responseMake = await brokerVhc.makeBooking(apiToken, responseStart);

            console.log("cURL enviado para o booking:", JSON.stringify(responseMake, null, 2));

            responseMake.rate = responseHasAvail.value.rate;
            delete responseMake.priceBase;
            delete responseMake.servicesMandatory;
            delete responseMake.message;
    
            console.log("Fim send bandeija - Booking ID final:", responseMake.bookingID);
            res.status(200).json(responseMake);
        } catch (e) {
            console.error("Erro send bandeija", e.message);
            let retorno = { status: "error", value: "Erro ao realizar chamada", error: e, message: e.message };
            res.status(500).json(retorno);
        }
    }

    public cancel = async (req: Request, res: Response) => {
        console.log("Inicio cancel bandeija")
        let bookingID = req.params.bookingID;

        let request = {
            bookingID: bookingID,
            result: ""
        }

        const brokerVhc = new BrokerVhc();
        await brokerVhc.init(); 

        let credentials = await brokerVhc.getCredential(req);
        let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom)
        let responseCancel = await brokerVhc.cancelBooking(apiToken ,request);
        
        console.log("Fim cancel bandeija")
        res.status(200)
            .json(responseCancel);
    }

    public get = async (req: Request, res: Response) => {
        console.log("Inicio get bandeija")        
        let bookingID = req.params.bookingID;

        const brokerVhc = new BrokerVhc();
        await brokerVhc.init(); 

        let credentials = await brokerVhc.getCredential(req);
        let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom)
        let responseGet = await brokerVhc.getBooking(apiToken ,bookingID);
        let rate = JSON.parse(responseGet.message);
        responseGet.rate = JSON.parse(rate);
        delete responseGet.message;
        delete responseGet.price;
        console.log("Fim get bandeija")
        res.status(200)
            .json(responseGet);
    }

    /**
     * @method hasAvail
     * @param  {req} - Request
     * @param  {res} - Response
     * @returns {retorno} - retorno
     */
    public hasAvail = async (req: Request) => {
        try{
            console.log("Inicio hasAvail");
            let retorno:any = {};
            let keyRateToken = req.params.keyRateToken;
            let rateToken:any = await this.redis.getKey(keyRateToken);
            if(rateToken != null && rateToken != undefined){
                let requestVhc = await this.util.convertRateTokenToPropertyDetailRequest(rateToken);         
                const brokerVhc = new BrokerVhc();
                await brokerVhc.init();

                console.log("Chamando serviços da VHC");            
                let credentials = await brokerVhc.getCredential(req);                
                let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom)                
                let response = await this.util.convertPropertyDetailResponse(await brokerVhc.getProperty(requestVhc, apiToken));


                //Inserir redis
                console.log("Montando rateToken para salvar no redis");            
                let houses:any = [];        
                let property = response;
                property.keyRateToken = keyRateToken;
                rateToken.keyRateToken = keyRateToken;
                rateToken.price = 0;
                rateToken.priceWithTax = 0;
                rateToken.priceWithoutTax = 0;
                rateToken.playerPriceWithTax = 0;
                rateToken.playerPriceWithoutTax = 0;                                

                await this.redis.setKey(keyRateToken, rateToken);
                await this.redis.deleteField(keyRateToken, "taxes");
                await this.redis.deleteField(keyRateToken, "rate");
                houses.push({keyRateToken: keyRateToken})

                rateToken.price = property.price.subtotal;
                rateToken.priceWithTax = property.price.total;
                rateToken.priceWithoutTax = property.price.subtotal;
                rateToken.playerPriceWithTax = property.price.total;
                rateToken.playerPriceWithoutTax = property.price.subtotal;

                await this.redis.setKey(keyRateToken, rateToken)

                let checkin = moment(rateToken.startDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss");
                let checkout = moment(rateToken.endDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss");            

                
                const token: any = req.headers['api-token']
                let plans = await brokerVhc.parcelas(req, token, checkin, checkout, rateToken.startZoneId, rateToken.endZoneId, rateToken.packageGroup, "HOU");
                if(plans != undefined){
                    response.maxInstallments = plans[0].maxInstallments
                }


                //Chamar serviço Drools para montar a precificação das propriedades retornadas no serviço
                //let requestDrools = await this.util.convertDroolsRequest(request, response, branchId, agentSign, jwt.endContry);            
                console.log("Chamar serviço Drools para montar a precificação da propriedade retornada no serviço");
                let responseDrools = await brokerVhc.precificacao(houses);  

                let a:any = moment(rateToken.startDate, 'YYYY-MM-DD').format('DD/MM/YYYY');                
                let b:any = moment(rateToken.endDate, 'YYYY-MM-DD').format('DD/MM/YYYY');
                a = moment(a, 'DD/MM/YYYY')
                b = moment(b, 'DD/MM/YYYY')                
                // let days = b.diff(a,'days') + 1; 
                let days = b.diff(a,'days');             
                delete property.price;                                        
                let propertyDrools = responseDrools.houses.filter((a:any)=>a.keyRateToken==property.keyRateToken)[0];        
                console.log(property.rate);
                if(propertyDrools != null){
                    property.rate = {};
                    property.rate.priceWithTax = propertyDrools.priceWithTax;
                    property.rate.priceWithoutTax = propertyDrools.priceWithoutTax;
                    property.rate.pricePerPaxWithTax = propertyDrools.pricePerPaxWithTax;
                    property.rate.pricePerPaxWithoutTax = propertyDrools.pricePerPaxWithoutTax;                    
                    //property.rate.days = days;
                    property.rate.pricePerDayWithTax = propertyDrools.priceWithTax / days;
                    property.rate.pricePerDayWithoutTax = propertyDrools.priceWithoutTax / days;
                    property.taxes = []
                    property.taxes[0] = propertyDrools.taxes[0];
                    rateToken.rate = JSON.stringify(property.rate);
                    await this.redis.setKey(keyRateToken, rateToken)
                }

                delete response.price;

                response.rate = JSON.parse(rateToken.rate);

                retorno = {status:"ok", value: property};            
            }else{
                retorno = {status:"error", value: "KeyRateToken não localizada"};
            }
            console.log("Fim hasAvail");            
            return retorno;
        }catch(e){
            console.error(e);
            console.error("Erro hasAvail");
            let retorno = {status:"error", value: "Erro ao realizar chamada", error: e};
            return retorno;            
        }
    }

}    
