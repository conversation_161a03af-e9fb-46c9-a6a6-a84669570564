

import Axios from "axios";
import { Request, Response } from 'express';


export default class ShoppingCartController
{
	private readonly axiosConfig: { [ key: string]: any };
	private startURL: string | undefined;
	private readonly gtwSecUserToken: string = "gtw-sec-user-token";
	private readonly jsonApp: string = "application/json";

	constructor()
	{
		this.startURL = process.env.MS_SHOPPING_CART_URL;

		if ( this.startURL != null && this.startURL.match( /\/$/ ) !== null )
		{
			this.startURL = this.startURL.replace( /\/$/, "");
		}

		this.axiosConfig  = 
		{
			baseURL: null,
			method: "patch",
			headers:
			{
				"Content-Type": this.jsonApp,
				"gtw-business-unit": "LOJ",
				[ this.gtwSecUserToken ] : null
			},
			data: null
		}

	}


	public async patch( req: Request, res: Response )
	{
		const response: { [ key: string]: any }  = { success: true };

		const reqBody = req.body;

		const incoming: { [ key: string]: any } = 
		{
			id: reqBody.id,
//			keyRateToken: reqBody.keyRateToken,
			gtwSecUserToken: reqBody.gtwSecUserToken,
			value: reqBody.value
		};

		for ( const key in incoming )
		{
			const thisIter = incoming[ key ];
			if ( thisIter == null )
			{
				response.success = false;
				response.error = `Please you must set ${ Object.keys( incoming ).toString() }`;
				res.status( 400 ).json( response );
				return;
			}
		}

		this.axiosConfig.headers[ this.gtwSecUserToken ] = incoming.gtwSecUserToken;
		let target: string = `${this.startURL}/cart/${incoming.id}`;
		response.targetUrl = target
		this.axiosConfig.baseURL = target;
		this.axiosConfig.data = incoming.value;
		console.log( "-> ShoppingCart API, data.to.be.sent:" );
		this.logConfig( this.axiosConfig );
		try
		{
			let httpResponse = await Axios.request( this.axiosConfig );
			response.fromTargetUrl = httpResponse.data;

			response.itemsLength = 
				response.fromTargetUrl && response.fromTargetUrl.items && response.fromTargetUrl.items.length ||
				"Could not calc items";

			res.json( response );
		}
		catch( error )
		{
			response.success = false;
			response.error = error;
			res.status(500).json( response );
		}

	}

	logConfig( config:any )
	{
		for ( let i in config )
		{
			if ( i !== "data")
			{
				console.log( i, config[ i ] );
			}
			else
			{
				console.log("bodyRequest to send is being omitted to help readability");
			}
		}
	}
}
