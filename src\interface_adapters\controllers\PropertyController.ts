import { Request, Response } from 'express';
import { ConfigOracleDB } from '../../frameworks_drivers/database/configOracleDB'
import BrokerVhc from '../../application_bussines_rules/brokers/brokerVhc'
import Util from '../../enterprise_bussines_rules/utils/util'
//import ConsulConsult from '../../frameworks_drivers/middlewares/consulConsult'
import Redis from '../../frameworks_drivers/middlewares/redis'
//import './../../env';
import JWTSecurity from '../../application_bussines_rules/security/jwtSecurity';
import moment from 'moment';
import uuid from 'uuid';
import ConsulConsult from '../../frameworks_drivers/middlewares/consulConsult';
// import ConsulConsult from '../../frameworks_drivers/middlewares/consulConsult';

//import {LogstashService} from '../../frameworks_drivers/middlewares/logStash'
/**
 * @class PropertyController
 * @classdesc Controller com as funções que retornam informações das propriedades/casas/aptos
 * <AUTHOR>
 */

export default class PropertyController {

    private configOracleDB = new ConfigOracleDB();
    //  private consulConsult = new ConsulConsult();
    private util = new Util();
    private redis = new Redis();
    // private consulConsult = new ConsulConsult();
    //private logger = new LogstashService();

    public getRedisPropertyKey = (propertyId: string, date: any = new Date()) => {
        const redisPropertyKey = `property-${propertyId}-${moment(date).format("YYYY-MM-DD")}`;
        const redisPropertyKeyBuff = new Buffer(redisPropertyKey, 'base64');
        const redisPropertyKeyToken = redisPropertyKeyBuff.toString('hex');

        return redisPropertyKeyToken;
    }

    public getRedisPropertyVisits = async (propertyId: string, daysCount: number = 1) => {
        let totalVisits = 0;

        for (let i = 0; i < daysCount; i++) {
            const dateLoop = moment(new Date()).subtract(i, 'days');
            const redisPropertyKey = this.getRedisPropertyKey(propertyId, dateLoop);
            const redisProperty = await this.redis.getKeyRaw(redisPropertyKey);

            totalVisits += parseInt(redisProperty?.visits) || 0;
        }

        return totalVisits;
    }

    public decode = async (req: Request, res: Response) => {
        const rateTokenHeader = req.headers['ratetoken'] as string;

        if (rateTokenHeader) {
            try {
            
                const rateToken = Buffer.from(rateTokenHeader, 'base64').toString('utf-8');
                console.log("Decoded rateToken: " + rateToken);
                
                return res.status(200).json({ decodedToken: rateToken });
            } catch (error) {
                console.error('Error decoding rateToken:', error);
                return res.status(400).json({ message: 'Invalid rateToken format' });
            }
        } else {
            console.log("rateToken not provided in headers");
            return res.status(400).json({ message: 'rateToken header is required' });
        }
    };

    /**
     * @method listProperties
     * @param  {req} - Request
     * @param  {res} - Response
     * @returns {retorno} - retorno
     * @description metodo utilizado para retornar as lista de propriedades da vhc
     */
    public listProperties = async (req: Request, res: Response) => {

        if (!req.headers['gtw-transaction-id']) req.headers['gtw-transaction-id'] = uuid();
        const transactioId = req.headers['gtw-transaction-id'];

        try {
            ////console.request = req;
            console.log("Inicio listar propriedades");
            let request = req.body;
            console.log("REQUEST: " + JSON.stringify(request));
            let branchId = req.params.branchId;
            let agentSign = req.params.agentSign;

            //Buscar cidade origem do sec-user-token
            console.log("Buscar cidade origem pela branch id")
            let jwt: any;

            let origem = await this.configOracleDB.getCidadeOrigem(branchId, transactioId);

            jwt =
            {
                startContry: origem.rows[0].CD_PAIS,
                startState: origem.rows[0].CD_ESTADO,
                startZoneId: origem.rows[0].CD_SEQUENCIA_WEBSERVICE
            }

            jwt.startDate = request.checkin;
            jwt.endDate = request.checkout;
            jwt.packageGroup = request.packageGroup;

            //Chamar serviços para busca de propriedades na VHC
            console.log("Chamando serviços da VHC");
            let requestVhc, credentials, apiToken, response: any, /*zones, zone,*/ types;
            let brokerVhc;
            let houses: any = [];
            try {
                requestVhc = await this.util.convertPropertyRequest(request);
                brokerVhc = new BrokerVhc();
                await brokerVhc.init();
                credentials = await brokerVhc.getCredential(req);
                apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom)
                const propertyList = await brokerVhc.propertyList(requestVhc, apiToken);

                if(!propertyList || propertyList.length < 1){
                    let retorno = { status: "ok", value: [], transactioId };
                    res.status(200).json(retorno);
                    return;
                }
                
                response = await this.util.convertListPropertyResponse(propertyList)

                const token: any = req.headers['api-token']


                let plans = await brokerVhc.parcelas(
                    req, token, moment(request.checkin, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss"),
                    moment(request.checkout, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss"),
                    jwt.startZoneId, jwt.startZoneId,
                    request.packageGroup, "HOU");

                if (plans && plans[0]
                    && plans[0].maxInstallments) response.installment = plans[0].maxInstallments


                // if (response) {
                //     const installment = await this.consulConsult.consultKey("installment");
                //     response.installment = (installment) ? parseInt(installment) : null;
                // }

                types = await brokerVhc.typeList(apiToken)

                //Inserir redis
                let destinations: object = {};
                let avoidAgain = false;

                console.log("Montando rateToken para salvar no redis");

                for (let i = 0; i < response.propertyList.length; i++) {
                    let property = response.propertyList[i];

                    const success = await this.fillJwtWithDestination(property, destinations, jwt, request.zoneName);

                    if (success === false && avoidAgain === false) {
                        response.propertyList.splice(i, 1);
                        --i;
                        avoidAgain = true;
                        continue;
                    }

                    avoidAgain = false;

                    let rateToken: any = {};
                    let data = property.id + property.idPmsExternal + moment(new Date()).format("YYYY-MM-DD") + Math.random();
                    let buff = new Buffer(data, 'base64');
                    let keyRateToken = buff.toString('hex');
                    property.keyRateToken = keyRateToken;
                    rateToken = await this.util.parsePropertyListRateToken(property, agentSign, branchId, request, jwt, types);

                    const totalVisits = await this.getRedisPropertyVisits(property.id, brokerVhc.ACELERATOR_DAYS_COUNT);

                    if (totalVisits > brokerVhc.ACELERATOR_HOUSES_VIEW) {
                        property.manyInterested = true;
                    }

                    try {
                        await this.redis.setKey(keyRateToken, rateToken);
                    }
                    catch (error) {
                        throw {

                            name: "Redis",
                            message: "Could not set key in Redis with keyRateToken:" +
                                keyRateToken + ", and rateToken:" +
                                rateToken,
                            jwt: jwt,

                            error: error
                        }
                    }
                    houses.push({ keyRateToken: keyRateToken })
                }



                //				zones = JSON.parse(await this.consulConsult.consultKey(process.env.ZONES));        
                //				zone = zones.filter((a:any)=>a.name==request.zoneName)[0];            
                //				response.zone = zone;
            }
            catch (error) {
                throw {
                    name: "VHCError",
                    error: error
                }
            }

            //Chamar serviço Drools para montar a precificação das propriedades retornadas no serviço
            //let requestDrools = await this.util.convertDroolsRequest(request, response, branchId, agentSign, jwt.endCountry);            
            console.log("Chamar serviço Drools para montar a precificação das propriedades retornada no serviço");
            //console.log(JSON.stringify(houses));
            let responseDrools: any = await brokerVhc.precificacao(houses);
            let a: any = moment(request.checkin, 'DD/MM/YYYY');
            let b: any = moment(request.checkout, 'DD/MM/YYYY');
            //let days = b.diff(a,'days') + 1;                    
            let days = b.diff(a, 'days');

            const consulConsult = new ConsulConsult();
            const zoneIdCvcDictionary = await consulConsult.consultKey("zoneIdCvcDictionary");

            response.propertyList.forEach((property: any) => {
                delete property.price;
                let propertyDrools = responseDrools.houses.filter((a: any) => a.keyRateToken == property.keyRateToken)[0];
                let rt: string | null = null;
                try {
                    rt = this.util.parsePropertyToRateToken(property, agentSign, branchId, request, jwt, propertyDrools, zoneIdCvcDictionary, "listProperties");
                } catch (error) {
                    console.log(error);
                }
                if (propertyDrools != null) {
                    if (rt !== null) {
                        property.rateToken = rt;
                    }

                    property.rate = {};
                    property.rate.priceWithTax = propertyDrools.priceWithTax;
                    property.rate.priceWithoutTax = propertyDrools.priceWithoutTax;
                    property.rate.pricePerPaxWithTax = propertyDrools.pricePerPaxWithTax;
                    property.rate.pricePerPaxWithoutTax = propertyDrools.pricePerPaxWithoutTax;
                    //property.rate.days = days;
                    property.rate.pricePerDayWithTax = property.rate.priceWithTax / days;
                    property.rate.pricePerDayWithoutTax = property.rate.priceWithoutTax / days;
                    property.taxes = propertyDrools.taxes;
                    return false;
                } else {
                    response.propertyList = response.propertyList.filter((a: any) => a.keyRateToken != property.keyRateToken);
                }
            });

            console.log("Fim listar propriedades");
            let retorno = { status: "ok", value: response, transactioId };
            res.status(200).json(retorno)
        }
        catch (err) {

            res.status(500)
                .json({ status: "error", value: err.message, transactioId });

        }
    }

    async fillJwtWithDestination(property: any, destinations: any, jwt: any, zoneName: string) {
        const key = `${property.city}.${property.state}`

        if (destinations.hasOwnProperty(key) === true) {
            const fromArchive = destinations[key];
            jwt.endCountry = fromArchive.country;
            jwt.endState = fromArchive.state;
            jwt.endZoneId = fromArchive.webserviceSequence;
            return true;
        }

        console.log(`Retrieving city using city(${property.city}) and state(${property.state})`)
        const tries =
            [
                async () => await this.configOracleDB.getCityInfo(property.city, property.state),
                async () => await this.configOracleDB.getCidadeDestino(zoneName)
            ]
        let destination;
        try {
            let acquired: boolean = false;
            for (let i = 0; i < tries.length; i++) {
                destination = await tries[i]()
                if
                    (
                    destination.rows.length > 0
                ) {
                    acquired = true;
                    break;
                }
            }

            if (acquired === false) {
                return acquired
            }

            jwt.endCountry = destination.rows[0].CD_PAIS;
            jwt.endState = destination.rows[0].CD_ESTADO;
            jwt.endZoneId = destination.rows[0].CD_SEQUENCIA_WEBSERVICE;

            destinations[key] =
            {
                country: jwt.endCountry,
                state: jwt.endState,
                webserviceSequence: jwt.endZoneId
            }
            return true;
        }
        catch (error) {
            throw {
                name: "DBError",
                message: "When getCidadeDestino",
                cityState: [property.city, property.state],
                getCidadeDestinoResponse: destination,
                error: error
            }
        }
    }

    /**
     * @method getProperty
     * @param  {req} - Request
     * @param  {res} - Response
     * @returns {retorno} - retorno
     */
    public getProperty = async (req: Request, res: Response) => {

        if (!req.headers['gtw-transaction-id']) req.headers['gtw-transaction-id'] = uuid();
        const transactioId = req.headers['gtw-transaction-id'];

        try {
            //console.request = req;
            console.log("Inicio buscar detalhe propriedade");
            let request = req.body;
            console.log("REQUEST: " + JSON.stringify(request));
            let branchId = req.params.branchId;
            let agentSign = req.params.agentSign;


            //Buscar cidade origem do sec-user-token
            console.log("Buscar cidade origem pela branch id")
            let origem = await this.configOracleDB.getCidadeOrigem(branchId, transactioId);
            let jwt: any = {
                startContry: origem.rows[0].CD_PAIS,
                startState: origem.rows[0].CD_ESTADO,
                startZoneId: origem.rows[0].CD_SEQUENCIA_WEBSERVICE
            }

            console.log("Chamando serviços da VHC");
            let requestVhc = await this.util.convertPropertyDetailRequest(request);
            const brokerVhc = new BrokerVhc();
            await brokerVhc.init();
            let credentials = await brokerVhc.getCredential(req);
            let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom)
            let getPropertyRawResponse = await brokerVhc.getProperty(requestVhc, apiToken);
            let response = this.util.convertPropertyDetailResponse(getPropertyRawResponse);



            let types = await brokerVhc.typeList(apiToken)

            const success = await this.fillJwtWithDestination(getPropertyRawResponse, [], jwt, request.zoneName);

            if (success === false) {
                res.status(404).json
                    (
                        {
                            message: `Could not acquire destination with city:${getPropertyRawResponse.city} 
						and state:${getPropertyRawResponse.state} and nor with zoneName:${request.zoneName}`
                        }
                    )
                return;
            }


            //Inserir redis
            console.log("Montando rateToken para salvar no redis");
            let houses: any = [];
            let property = response;
            let rateToken: any = {};

            const redisPropertyKey = this.getRedisPropertyKey(property.id);
            const redisProperty = await this.redis.getKeyRaw(redisPropertyKey);

            const totalVisits = (redisProperty?.visits ?? 0) + 1;

            await this.redis.setKeyRaw(redisPropertyKey, {
                visits: totalVisits,
                property
            });

            let data = property.id + property.idPmsExternal + moment(new Date()).format("YYYY-MM-DD") + Math.random();
            let buff = new Buffer(data, 'base64');
            let keyRateToken = buff.toString('hex');
            property.keyRateToken = keyRateToken;
            rateToken = await this.util.parsePropertyDetailRateToken(property, agentSign, branchId, request, jwt, types);

            await this.redis.setKey(keyRateToken, rateToken);

            // let taxes = null;
            // if(property.price.total != property.price.subtotal){
            //     let taxFee = property.price.total - property.price.subtotal;
            //     //let percent = (taxFee/property.price.subtotal) * 100
            //     taxes = [{'description': 'tax_fee', 'amount':taxFee}];
            //     //taxes = "[Tax(code=E, description=tax_fee, percent=" + percent + ", amount=" + taxFee + ")]";            
            // }             
            houses.push({ keyRateToken: keyRateToken })

            //Chamar serviço Drools para montar a precificação das propriedades retornadas no serviço
            //let requestDrools = await this.util.convertDroolsRequest(request, response, branchId, agentSign, jwt.endCountry); 
            console.log("Chamar serviço Drools para montar a precificação da propriedade retornada no serviço");
            let responseDrools: any = await brokerVhc.precificacao(houses);
            let a: any = moment(request.checkin, 'DD/MM/YYYY');
            let b: any = moment(request.checkout, 'DD/MM/YYYY');
            // let days = b.diff(a,'days') + 1;
            let days = b.diff(a, 'days');
            delete property.price;

            const consulConsult = new ConsulConsult();
            const zoneIdCvcDictionary = await consulConsult.consultKey("zoneIdCvcDictionary");
            let propertyDrools = responseDrools.houses.filter((a: any) => a.keyRateToken == property.keyRateToken)[0];
            let rt: string | null = null;
            try {
                rt = this.util.parsePropertyToRateToken(property, agentSign, branchId, request, jwt, propertyDrools, zoneIdCvcDictionary, "getProperty");
            } catch (error) {
                console.log(error);
            }
            if (propertyDrools != null) {
                if (rt !== null) {
                    property.rateToken = rt;
                }

                property.rate = {};
                property.rateToken = rt;
                property.rate.priceWithTax = propertyDrools.priceWithTax;
                property.rate.priceWithoutTax = propertyDrools.priceWithoutTax;
                property.rate.pricePerPaxWithTax = propertyDrools.pricePerPaxWithTax;
                property.rate.pricePerPaxWithoutTax = propertyDrools.pricePerPaxWithoutTax;
                //property.rate.days = days;            
                property.rate.pricePerDayWithTax = property.rate.priceWithTax / days;
                property.rate.pricePerDayWithoutTax = property.rate.priceWithoutTax / days;
                property.taxes = propertyDrools.taxes;
                rateToken.rate = JSON.stringify(property.rate);
                rateToken.taxes = "[";
                if (propertyDrools.taxes != null) {
                    for (let i = 0; i < propertyDrools.taxes.length; i++) {
                        if (i > 0) {
                            rateToken.taxes = rateToken.taxes + ",";
                        }
                        let tax = propertyDrools.taxes[i];
                        let pctAppliedToCost = tax.pctAppliedToCost != null ? tax.pctAppliedToCost : null;
                        let pctAppliedToTotal = tax.pctAppliedToTotal != null ? tax.pctAppliedToTotal : null;
                        let productAppliedTax = tax.productAppliedTax != null ? tax.productAppliedTax : null;
                        let rph = tax.rph != null ? tax.rph : null;
                        rateToken.taxes = rateToken.taxes + "Tax(code=" + tax.code + ", description=" + tax.description + ", percent=" + tax.percent + ", amount=" + tax.amount + " , pctAppliedToCost=" + pctAppliedToCost + " , pctAppliedToTotal=" + pctAppliedToTotal + " , productAppliedTax=" + productAppliedTax + " , rph=" + rph + ")";
                    }
                }
                rateToken.taxes = rateToken.taxes + "]";
                await this.redis.setKey(keyRateToken, rateToken);
            }




            const token: any = req.headers['api-token']

            let plans = await brokerVhc.parcelas
                (
                    req, token, moment(request.checkin, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss"),
                    moment(request.checkout, 'DD/MM/YYYY').format("YYYY-MM-DDThh:mm:ss"),
                    jwt.startZoneId, jwt.endZoneId,
                    request.packageGroup, "HOU"
                )
                .then
                (
                    response => {
                        return { success: true, response: response, transactioId }
                    }
                )
                .catch
                (
                    e => {
                        return { success: false, response: e, transactioId }
                    }
                );

            if
                (
                plans.success === true &&
                plans.response !== undefined &&
                plans.response.length > 0
            ) {
                response.maxInstallments = plans.response[0].maxInstallments
                property.installment = response.maxInstallments;
            }

            // if (property) {
            //     const installment = await this.consulConsult.consultKey("installment");
            //     property.installment = (installment) ? parseInt(installment) : null;
            // }

            let retorno = { status: "ok", value: property, transactioId };

            console.log("Fim buscar detalhe propriedade");

            res.status(200).json(retorno);

        } catch (e) {
            console.error(e);
            console.error("Erro buscar detalhe propriedade");
            let retorno = { status: "error", transactioId, value: "Erro ao realizar chamada", error: e };
            res.status(500).json(retorno)
        }
    }

    /**
     * @method firstGet
     * @param  {req} - Request
     * @param  {res} - Response
     * @returns {retorno} - retorno
     */
    public firstGet = async (req: Request, res: Response) => {
        let branchId = req.params.branchId;
        let value = await this.configOracleDB.getCidadeOrigem(branchId, null);
        let jwt = {
            secUserToken: req.headers['api-token'],
            startContry: value.rows[0].CD_PAIS,
            startState: value.rows[0].CD_ESTADO,
            startZoneId: value.rows[0].CD_SEQUENCIA_WEBSERVICE
        }
        let token = JWTSecurity.rampUp(jwt);
        res.header("api-token", token);
        let retorno = { status: "ok", token: token };
        res.status(200)
            .json(retorno)
    }

    /**
     * @method hasAvail
     * @param  {req} - Request
     * @param  {res} - Response
     * @returns {retorno} - retorno
     */
    public hasAvail = async (req: Request, res: Response) => {
        try {
            console.log("Inicio hasAvail");
            let retorno: any = {};
            let keyRateToken = req.params.keyRateToken;
            let rateToken: any = await this.redis.getKey(keyRateToken);
            if (rateToken != null && rateToken != undefined) {
                let requestVhc = await this.util.convertRateTokenToPropertyDetailRequest(rateToken);
                const brokerVhc = new BrokerVhc();
                await brokerVhc.init();

                console.log("Chamando serviços da VHC");
                let credentials = await brokerVhc.getCredential(req);
                let apiToken = await brokerVhc.login(credentials.username, credentials.password, credentials.authenticationFrom)
                let response = await this.util.convertPropertyDetailResponse(await brokerVhc.getProperty(requestVhc, apiToken));


                //Inserir redis
                console.log("Montando rateToken para salvar no redis");
                let houses: any = [];

                let property = response;

                property.keyRateToken = keyRateToken;
                rateToken.keyRateToken = keyRateToken;
                rateToken.price = 0;
                rateToken.priceWithTax = 0;
                rateToken.priceWithoutTax = 0;
                rateToken.playerPriceWithTax = 0;
                rateToken.playerPriceWithoutTax = 0;

                await this.redis.setKey(keyRateToken, rateToken);
                await this.redis.deleteField(keyRateToken, "taxes");
                await this.redis.deleteField(keyRateToken, "rate");
                houses.push({ keyRateToken: keyRateToken })

                rateToken.price = property.price.subtotal;
                rateToken.priceWithTax = property.price.total;
                rateToken.priceWithoutTax = property.price.subtotal;
                rateToken.playerPriceWithTax = property.price.total;
                rateToken.playerPriceWithoutTax = property.price.subtotal;
                if (property.price.total != property.price.subtotal) {
                    let taxFee = property.price.total - property.price.subtotal;
                    let percent = (taxFee / property.price.subtotal) * 100
                    //taxes = [{'description': 'tax_fee', 'amount':taxFee}];
                    rateToken.taxes = "[Tax(code=E, description=tax_fee, percent=" + percent.toFixed(2) + ", amount=" + taxFee.toFixed(2) + ")]";
                }

                await this.redis.setKey(keyRateToken, rateToken)

                let checkin = moment(rateToken.startDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss");
                let checkout = moment(rateToken.endDate, 'YYYY-MM-DD').format("YYYY-MM-DDThh:mm:ss");

                response.checkIn = checkin;
                response.checkOut = checkout;

                const token: any = req.headers['api-token']
                let plans = await brokerVhc.parcelas(req, token, checkin, checkout, rateToken.startZoneId, rateToken.endZoneId, rateToken.packageGroup, "HOU");
                if (plans != undefined) {
                    response.maxInstallments = plans[0].maxInstallments
                }


                //Chamar serviço Drools para montar a precificação das propriedades retornadas no serviço
                //let requestDrools = await this.util.convertDroolsRequest(request, response, branchId, agentSign, jwt.endCountry);            
                console.log("Chamar serviço Drools para montar a precificação da propriedade retornada no serviço");
                console.log(houses);
                let responseDrools = await brokerVhc.precificacao(houses);

                let a: any = moment(rateToken.startDate, 'YYYY-MM-DD').format('DD/MM/YYYY');
                let b: any = moment(rateToken.endDate, 'YYYY-MM-DD').format('DD/MM/YYYY');
                a = moment(a, 'DD/MM/YYYY')
                b = moment(b, 'DD/MM/YYYY')
                // let days = b.diff(a,'days') + 1;
                let days = b.diff(a, 'days');
                delete property.price;
                let propertyDrools = responseDrools.houses.filter((a: any) => a.keyRateToken == property.keyRateToken)[0];
                if (propertyDrools != null) {
                    property.rate = {};
                    property.rate.priceWithTax = propertyDrools.priceWithTax;
                    property.rate.priceWithoutTax = propertyDrools.priceWithoutTax;
                    property.rate.pricePerPaxWithTax = propertyDrools.pricePerPaxWithTax;
                    property.rate.pricePerPaxWithoutTax = propertyDrools.pricePerPaxWithoutTax;
                    //property.rate.days = days;
                    property.rate.pricePerDayWithTax = propertyDrools.priceWithTax / days;
                    property.rate.pricePerDayWithoutTax = propertyDrools.priceWithoutTax / days;
                    property.taxes = []
                    property.taxes = propertyDrools.taxes;
                    rateToken.rate = JSON.stringify(property.rate);
                    rateToken.taxes = "[";
                    if (propertyDrools.taxes != null) {
                        for (let i = 0; i < propertyDrools.taxes.length; i++) {
                            if (i > 0) {
                                rateToken.taxes = rateToken.taxes + ",";
                            }
                            let tax = propertyDrools.taxes[i];
                            let pctAppliedToCost = tax.pctAppliedToCost != null ? tax.pctAppliedToCost : null;
                            let pctAppliedToTotal = tax.pctAppliedToTotal != null ? tax.pctAppliedToTotal : null;
                            let productAppliedTax = tax.productAppliedTax != null ? tax.productAppliedTax : null;
                            let rph = tax.rph != null ? tax.rph : null;
                            rateToken.taxes = rateToken.taxes + "Tax(code=" + tax.code + ", description=" + tax.description + ", percent=" + tax.percent + ", amount=" + tax.amount + " , pctAppliedToCost=" + pctAppliedToCost + " , pctAppliedToTotal=" + pctAppliedToTotal + " , productAppliedTax=" + productAppliedTax + " , rph=" + rph + ")";
                        }
                    }
                    rateToken.taxes = rateToken.taxes + "]";
                    await this.redis.setKey(keyRateToken, rateToken)
                }

                delete response.price;
                delete property.rate;

                response.rates = JSON.parse(rateToken.rate);
                response.paxs = rateToken.paxs;

                retorno = { status: "ok", value: property };
            } else {
                retorno = { status: "error", value: "KeyRateToken não localizada" };
            }
            console.log("Fim hasAvail");
            res.status(200)
                .json(retorno)
        } catch (e) {
            console.error(e);
            console.error("Erro hasAvail");
            let retorno = { status: "error", value: "Erro ao realizar chamada", error: e };
            res.status(500)
                .json(retorno)
        }
    }
}
