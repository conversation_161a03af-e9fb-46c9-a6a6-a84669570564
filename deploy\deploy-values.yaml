environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 128Mi
                cpu: 50m
              limits:
                memory: 256Mi
                cpu: 200m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-bff
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: VAULT_HOST
                value: vault-dev.services.cvc.com.br
              - name: SECRET_JWT
                value: "1234567890123456"
              - name: DECRYPT_KEY
                value: decrypt_key
              - name: LOG_PORT
                value: "12201"
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: BASE_URL_VHC
                value: base_url_vhc
              - name: BASE_URL_VAULT
                value: base_url_vault
              - name: BASE_URL_VAULT_CREDENTIAL
                value: base_url_vault_credential
              - name: ACELERATOR_HOUSES_VIEW
                value: acelerator_houses_view
              - name: BASE_URL_PAYMMENT
                value: base_url_paymment
              - name: CONSUL_URL
                value: consul-dev.services.cvc.com.br
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_PORT
                value: "8500"
              - name: VHC_X_API_KEY
                value: vhc_x_api_key
              - name: ZONES
                value: zones
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: NODE_ENV
                value: ti
              - name: X_VAULT_TOKEN
                value: x_vault_token
              - name: X_VAULT_CREDENTIAL_TOKEN
                value: x_vault_credential_token
              - name: MS_SHOPPING_CART_URL
                value: https://ms-shopping-cart.k8s-ti-cvc.com.br/
              - name: ACELERATOR_DAYS_COUNT
                value: acelerator_days_count
              - name: PREFIX
                value: corp-wl-vhs-bff
              - name: BASE_URL_CREDENTIAL
                value: base_url_credential
              - name: DB_CONNECTION
                value: db_connection
              - name: BASE_URL_DROOLS
                value: base_url_drools
              - name: LOG_HOST
                value: logstash-ti.services.cvc.com.br
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 128Mi
                cpu: 50m
              limits:
                memory: 256Mi
                cpu: 200m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-bff
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_URL
                value: consul-qa.services.cvc.com.br
              - name: ZONES
                value: zones
              - name: BASE_URL_VAULT
                value: base_url_vault
              - name: BASE_URL_DROOLS
                value: base_url_drools
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: NODE_ENV
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: DB_CONNECTION
                value: db_connection
              - name: BASE_URL_VAULT_CREDENTIAL
                value: base_url_vault_credential
              - name: CONSUL_PORT
                value: "8500"
              - name: BASE_URL_VHC
                value: base_url_vhc
              - name: X_VAULT_CREDENTIAL_TOKEN
                value: x_vault_credential_token
              - name: BASE_URL_PAYMMENT
                value: base_url_paymment
              - name: LOG_PORT
                value: "12201"
              - name: VAULT_SCHEME
                value: http
              - name: SECRET_JWT
                value: "1234567890123456"
              - name: BASE_URL_CREDENTIAL
                value: base_url_credential
              - name: VHC_X_API_KEY
                value: vhc_x_api_key
              - name: DECRYPT_KEY
                value: decrypt_key
              - name: X_VAULT_TOKEN
                value: x_vault_token
              - name: ACELERATOR_DAYS_COUNT
                value: acelerator_days_count
              - name: MS_SHOPPING_CART_URL
                value: https://ms-shopping-cart.k8s-qa-cvc.com.br/
              - name: ACELERATOR_HOUSES_VIEW
                value: acelerator_houses_view
              - name: PREFIX
                value: corp-wl-vhs-bff
              - name: LOG_HOST
                value: logstash-qa.services.cvc.com.br
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 128Mi
                cpu: 50m
              limits:
                memory: 256Mi
                cpu: 200m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-bff
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_URL
                value: consul-qa.services.cvc.com.br
              - name: ZONES
                value: zones
              - name: BASE_URL_VAULT
                value: base_url_vault
              - name: BASE_URL_DROOLS
                value: base_url_drools
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: NODE_ENV
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: DB_CONNECTION
                value: db_connection
              - name: BASE_URL_VAULT_CREDENTIAL
                value: base_url_vault_credential
              - name: CONSUL_PORT
                value: "8500"
              - name: BASE_URL_VHC
                value: base_url_vhc
              - name: X_VAULT_CREDENTIAL_TOKEN
                value: x_vault_credential_token
              - name: BASE_URL_PAYMMENT
                value: base_url_paymment
              - name: LOG_PORT
                value: "12201"
              - name: VAULT_SCHEME
                value: http
              - name: SECRET_JWT
                value: "1234567890123456"
              - name: BASE_URL_CREDENTIAL
                value: base_url_credential
              - name: VHC_X_API_KEY
                value: vhc_x_api_key
              - name: DECRYPT_KEY
                value: decrypt_key
              - name: X_VAULT_TOKEN
                value: x_vault_token
              - name: ACELERATOR_DAYS_COUNT
                value: acelerator_days_count
              - name: MS_SHOPPING_CART_URL
                value: https://ms-shopping-cart.k8s-qa-cvc.com.br/
              - name: ACELERATOR_HOUSES_VIEW
                value: acelerator_houses_view
              - name: PREFIX
                value: corp-wl-vhs-bff
              - name: LOG_HOST
                value: logstash-qa.services.cvc.com.br
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 128Mi
                cpu: 50m
              limits:
                memory: 256Mi
                cpu: 200m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-bff
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: PREFIX
                value: corp-wl-vhs-bff
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: BASE_URL_CREDENTIAL
                value: base_url_credential
              - name: VHC_X_API_KEY
                value: vhc_x_api_key
              - name: X_VAULT_TOKEN
                value: x_vault_token
              - name: LOG_HOST
                value: logstash.services.cvc.com.br
              - name: CONSUL_URL
                value: consul-prod.services.cvc.com.br
              - name: SECRET_JWT
                value: "1234567890123456"
              - name: BASE_URL_VAULT_CREDENTIAL
                value: base_url_vault_credential
              - name: ACELERATOR_DAYS_COUNT
                value: acelerator_days_count
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: DECRYPT_KEY
                value: decrypt_key
              - name: BASE_URL_VAULT
                value: base_url_vault
              - name: DB_CONNECTION
                value: db_connection
              - name: BASE_URL_DROOLS
                value: base_url_drools
              - name: NODE_ENV
                value: prod
              - name: ZONES
                value: zones
              - name: VAULT_SCHEME
                value: http
              - name: X_VAULT_CREDENTIAL_TOKEN
                value: x_vault_credential_token
              - name: CONSUL_PORT
                value: "8500"
              - name: ACELERATOR_HOUSES_VIEW
                value: acelerator_houses_view
              - name: BASE_URL_VHC
                value: base_url_vhc
              - name: BASE_URL_PAYMMENT
                value: base_url_paymment
              - name: MS_SHOPPING_CART_URL
                value: https://ms-shopping-cart.k8s-cvc.com.br/
              - name: LOG_PORT
                value: "12201"
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 128Mi
                cpu: 50m
              limits:
                memory: 256Mi
                cpu: 200m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-wl-vhs-bff
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: PREFIX
                value: corp-wl-vhs-bff
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: BASE_URL_CREDENTIAL
                value: base_url_credential
              - name: VHC_X_API_KEY
                value: vhc_x_api_key
              - name: X_VAULT_TOKEN
                value: x_vault_token
              - name: LOG_HOST
                value: logstash.services.cvc.com.br
              - name: CONSUL_URL
                value: consul-prod.services.cvc.com.br
              - name: SECRET_JWT
                value: "1234567890123456"
              - name: BASE_URL_VAULT_CREDENTIAL
                value: base_url_vault_credential
              - name: ACELERATOR_DAYS_COUNT
                value: acelerator_days_count
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: DECRYPT_KEY
                value: decrypt_key
              - name: BASE_URL_VAULT
                value: base_url_vault
              - name: DB_CONNECTION
                value: db_connection
              - name: BASE_URL_DROOLS
                value: base_url_drools
              - name: NODE_ENV
                value: prod
              - name: ZONES
                value: zones
              - name: VAULT_SCHEME
                value: http
              - name: X_VAULT_CREDENTIAL_TOKEN
                value: x_vault_credential_token
              - name: CONSUL_PORT
                value: "8500"
              - name: ACELERATOR_HOUSES_VIEW
                value: acelerator_houses_view
              - name: BASE_URL_VHC
                value: base_url_vhc
              - name: BASE_URL_PAYMMENT
                value: base_url_paymment
              - name: MS_SHOPPING_CART_URL
                value: https://ms-shopping-cart.k8s-cvc.com.br/
              - name: LOG_PORT
                value: "12201"
