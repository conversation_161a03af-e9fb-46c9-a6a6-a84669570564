import { Request, Response, Router } from 'express';
import PropertyController from '../../interface_adapters/controllers/PropertyController';
import ZoneController from '../../interface_adapters/controllers/ZoneController';
import AmenityController from '../../interface_adapters/controllers/AmenityController';
import TrayController from '../../interface_adapters/controllers/TrayController';
import {verifyJWT}  from '../middlewares/verifyJWT';
import ShoppingCartController from "../../interface_adapters/controllers/ShoppingCartController";

const propertyController: PropertyController = new PropertyController();
const zoneController: ZoneController = new ZoneController();
const amenityController: AmenityController = new AmenityController();
const trayController: TrayController = new TrayController();
const routes = Router();

// PUBLIC ROUTES
routes.get('/health', (req: Request, res: Response) => {
    req;
    res.json
	(
		{ 
			status: 'OK',
			version: "1.40",
			environment: process.env.NODE_ENV,
			descriptionStackChronoReversed: 
			[
				"Do not let brokerVhc plans fault to halt details, revisited",
				"<NAME_EMAIL> at tray/send",
				"parseInt on bathrooms hasAvail",
				"Do not let brokerVhc plans fault to halt details",
				"Details for Maraú",
				"HOU replaces HOT",
				"shopping-cart should not return 200 when not successful",
				"Try with zoneName if the default does not work, otherwise remove the property"
			]
		}
	);
});
// PRIVATE ROUTES
routes.get('/api/decode', propertyController.decode);
routes.post('/api/property', verifyJWT, propertyController.listProperties);
routes.post('/api/property/detalhe', verifyJWT, propertyController.getProperty);
routes.post('/api/property/details', verifyJWT, propertyController.getProperty);
routes.get('/api/property/hasAvail/:keyRateToken', verifyJWT, propertyController.hasAvail);
routes.get('/api/zone', zoneController.getZone);
routes.get('/api/amenity', amenityController.getAmenities);
routes.post('/api/tray/send', verifyJWT, trayController.send);
routes.delete('/api/tray/cancel/:bookingID', verifyJWT, trayController.cancel);
routes.get('/api/tray/:bookingID', verifyJWT, trayController.get);

const shoppingCart = new ShoppingCartController();
routes.post( "/api/shopping-cart", shoppingCart.patch.bind( shoppingCart ) );

export default routes;


