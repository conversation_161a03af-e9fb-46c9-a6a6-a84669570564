import Axios, { AxiosRequestConfig } from 'axios';
import { Request } from 'express';
import AesDecrypt from '../../frameworks_drivers/middlewares/aesDecrypt';
import ConsulConsult from '../../frameworks_drivers/middlewares/consulConsult';
import logger from '../../frameworks_drivers/middlewares/logger/log';
import { StopWatch } from '../../frameworks_drivers/middlewares/logger/stopwatch';
//import './../../env';

//import jwt from 'jsonwebtoken';

/**
 * @class BrokerVhc
 * @classdesc Classe que gerencias o usuario e o login
 * <AUTHOR>
 */
export default class BrokerVhc {

    public BASE_URL: any;
    public BASE_URL_CREDENTIAL: any
    public X_API_KEY: any;
    public KEY: any;
    public BASE_URL_PAYMMENT: any
    public BASE_URL_DROOLS: any
    public ACELERATOR_HOUSES_VIEW: any;
    public ACELERATOR_DAYS_COUNT: any;

    public async init(){        
        let consulConsult = new ConsulConsult();         
        this.BASE_URL = await consulConsult.consultKey(process.env.BASE_URL_VHC);        
        this.BASE_URL_CREDENTIAL = await consulConsult.consultKey(process.env.BASE_URL_CREDENTIAL);        
        this.X_API_KEY = "90187e16-a2af-494a-a89e-5328e226107a";
        this.KEY = process.env.SPRING_DECRYPTY;        
        this.BASE_URL_PAYMMENT = await consulConsult.consultKey(process.env.BASE_URL_PAYMMENT);        
        this.BASE_URL_DROOLS = await consulConsult.consultKey(process.env.BASE_URL_DROOLS);     
        this.ACELERATOR_HOUSES_VIEW = parseInt(await consulConsult.consultKey(process.env.ACELERATOR_HOUSES_VIEW)) || 1;
        this.ACELERATOR_DAYS_COUNT = parseInt(await consulConsult.consultKey(process.env.ACELERATOR_DAYS_COUNT)) || 1;
    }

    private printCurlCommand(config: AxiosRequestConfig) {
        try {
            const method = config.method?.toUpperCase() || 'GET'; // Método HTTP
            const url = config.baseURL || ''; // URL da requisição
            const headers = config.headers || {}; // Headers
            const data = config.data ? JSON.stringify(config.data) : null; // Corpo da requisição
    
            // Monta os headers no formato cURL
            const headersString = Object.entries(headers)
                .map(([key, value]) => `-H "${key}: ${value}"`)
                .join(' \\\n');
    
            // Corpo da requisição como JSON puro
            const dataString = data ? `--data-raw '${data}'` : '';
    
            // Comando cURL completo
            const curlCommand = `
    ========== CURL ==========
    curl -X ${method} "${url}" \\
    ${headersString} \\
    ${dataString}
    ========================
    `;
    
            console.log(curlCommand); // Imprime o comando cURL no console
        } catch (error) {
            console.error('Erro ao gerar o comando cURL:', error);
        }
    }   

    private aesDecrypt = new AesDecrypt();

    /**
     * @method login
     * @param username 
     * @param password 
     */
    public async login(username: string, password: string, authenticationFrom: any) {

        const url = this.BASE_URL + '/api/Login';

        let objLogger: any = {
            operation: "VHC.GET.LOGIN",
            url
        };

        const timer = new StopWatch();
        timer.start();

        try {


            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "POST",
                data: {
                    username,
                    password,
                    authenticationFrom,
                }
            }

            objLogger = {
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de login");

            this.printCurlCommand(config);

            const { data } = await Axios(config);

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de login concluida com sucesso");


            return data.apiToken;

        }
        catch (error) {

            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de login ", error.message);

            throw new Error(error);

        }
    }

    /**
     * @method zoneList     
     */
    public async zoneList(language: string, apiToken: string) {

        const url = this.BASE_URL + '/api/Zone/List?language=' + language;

        let objLogger: any = {
            operation: "BFF.GET.ZONES",
            url
        };

        const timer = new StopWatch();
        timer.start();

        try {

            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "GET",
                headers: {
                    Authorization: 'Bearer ' + apiToken,
                    "X-Api-Key": this.X_API_KEY
                },
                data: {
                }
            }

            objLogger = {
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de zones");

            const { data } = await Axios(config);

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de zones concluida com sucesso");

            return data;

        } catch (error) {

            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de zones ", error.message);

            throw new Error(error);
        }

    }

    /**
     * @method propertyList     
     */
    public async propertyList(request: any, apiToken: string) {

        const url = this.BASE_URL + '/api/Property/List';

        let objLogger: any = {
            operation: "BFF.GET.PROPERTIES",
            url
        };

        const timer = new StopWatch();
        timer.start();

        try {

            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "POST",
                headers: {
                    Authorization: 'Bearer ' + apiToken,
                    "X-Api-Key": this.X_API_KEY
                },
                data: request
            }

            objLogger = {
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de properties");

            // Printa o Curl para debug
            this.printCurlCommand(config);

            const { data } = await Axios(config);

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de properties concluida com sucesso");

            if (!data || data.length === 0) return [];
            return data;

        }
        catch (error) {


            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de properties ", error.message);

            throw new Error(error);

        }

    }

    /**
     * @method getProperty     
     */
    public async getProperty(request: any, apiToken: string) {

        const url = this.BASE_URL + '/api/Property/Get';

        let objLogger: any = {
            operation: "BFF.GET.PROPERTY",
            url
        };

        const timer = new StopWatch();
        timer.start();


        try {

            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "POST",
                headers: {
                    Authorization: 'Bearer ' + apiToken,
                    "X-Api-Key": this.X_API_KEY
                },
                data: request
            }

            objLogger = {
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de property");

            const { data } = await Axios(config);

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de property concluida com sucesso");

            return data;

        }
        catch (error) {

            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de property ", error.message);

            throw new Error(error);
        }


    }

    private getConsulByCode = async (code) => {

        let consulConsult = new ConsulConsult();

        try {

            return await consulConsult.consultKey(code);

        } catch (error) {

            return null;
        }

    }

    /**
     * @method getCredential     
     */
    public async getCredential(request: Request) {



        let gtwAgentSing = request.headers['Gtw-Agent-Sign'] != undefined ? request.headers['Gtw-Agent-Sign'] : request.headers['gtw-agent-sign'];
        let transactionId = request.headers['Gtw-Transaction-Id'] != undefined ? request.headers['Gtw-Transaction-Id'] : request.headers['gtw-transaction-id'];
        const url = this.BASE_URL_CREDENTIAL + gtwAgentSing + '/1?transactionId=' + transactionId;

        let objLogger: any = {
            operation: "BFF.GET.CREDENTIALS",
            url
        };

        const timer = new StopWatch();
        timer.start();

        try {

            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "GET",
                headers: { 'Gtw-Transaction-Id': transactionId },
            }

            objLogger = {
                url,
                transactionId,
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de credentials");

            // Log do comando CURL para debug
            this.printCurlCommand(config);

            const byPassCredential = await this.getConsulByCode("byPassCredential");

            let dataCredential = {
                token: null
            };

            if (byPassCredential && byPassCredential === "true") {

                const token = await this.getConsulByCode("credential");

                dataCredential = {
                    token
                };

            } else {

                let { data } = await Axios(config);
                dataCredential = data;

            }

            // let { data } = await Axios(config);
            let decrypt = this.aesDecrypt.decrypt(this.KEY, dataCredential.token);
            let credentials = {
                username: decrypt.split(':')[0],
                password: decrypt.split(':')[1],
                authenticationFrom: 1
            }

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de credentials concluida com sucesso");

            return credentials;

        }
        catch (error) {

            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de credentials ", error.message);

            throw new Error(error);


        }
    }

    /**
     * @method amenityList     
     */
    public async amenityList(language: string, apiToken: string) {
        const config: AxiosRequestConfig = {
            baseURL: this.BASE_URL + '/api/Amenity/List?language=' + language,
            method: "GET",
            headers: {
                Authorization: 'Bearer ' + apiToken,
                "X-Api-Key": this.X_API_KEY
            },
            data: {
            }
        }
        const { data } = await Axios(config);
        return data;
    }

    /**
     * @method typeList     
     */
    public async typeList(apiToken: string) {

        const url = this.BASE_URL + '/api/Type/List';

        let objLogger: any = {
            operation: "BFF.GET.TYPES",
            url
        };

        const timer = new StopWatch();
        timer.start();

        try {

            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "GET",
                headers: {
                    Authorization: 'Bearer ' + apiToken,
                    "X-Api-Key": this.X_API_KEY
                },
                data: {
                }
            }

            objLogger = {
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de types");

            const { data } = await Axios(config);

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de types concluida com sucesso");


            return data;

        }
        catch (error) {

            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de types ", error.message);

            throw new Error(error);

        }

    }

    public async parcelas(request: any, token: any, startDate: any, endDate: any, startZoneId: any, endZoneId: any, packageGroup: any, productType: any) {

        let gtwSecUserToken = token;
        let gtwTransactionId = request.headers['Gtw-Transaction-Id'] != undefined ? request.headers['Gtw-Transaction-Id'] : request.headers['gtw-transaction-id'];;
        let params = "?endDate=" + encodeURIComponent(endDate) + "&endZoneId=" + endZoneId + "&packageGroup=" + packageGroup + "&productType=" + productType + "&startDate=" + encodeURIComponent(startDate) + "&startZoneId=" + startZoneId;
        const url = this.BASE_URL_PAYMMENT + params;

        let objLogger: any = {
            operation: "BFF.GET.PARCELAS",
            url,
            endDate,
            endZoneId,
            packageGroup,
            productType,
            startDate,
            startZoneId
        };

        const timer = new StopWatch();
        timer.start();

        try {

            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "GET",
                headers: { 'Gtw-Sec-User-Token': gtwSecUserToken, 'Gtw-Transaction-Id': gtwTransactionId }
            }

            objLogger = {
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de finan");

            const { data } = await Axios(config);

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de finan concluida com sucesso");

            return data.plans;
        }
        catch (error) {

            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de finan ", error.message);

            throw new Error(error);

        }

    }

    public async precificacao(request: any) {

        const url = this.BASE_URL_DROOLS;

        let objLogger: any = {
            operation: "BFF.GET.PRECIFICAÇÃO",
            url
        };

        const timer = new StopWatch();
        timer.start();

        try {

            const config: AxiosRequestConfig = {
                baseURL: url,
                method: "POST",
                data: { "houses": request }
            }

            objLogger = {
                duration: timer.getElapsedMilliseconds()
            };

            logger.info(objLogger, "Iniciando consulta a api de precificação");

            this.printCurlCommand(config);

            const { data } = await Axios(config);

            objLogger.duration = timer.getElapsedMilliseconds();;
            objLogger.short_message = "success";
            logger.info(objLogger, "Consulta  a api de precificação concluida com sucesso");

            return data;

        }
        catch (error) {

            const { message } = error;
            const shortMessage = message && message.indexOf("timeout") >= 0 ? "timeout" : "error";
            objLogger["short_message"] = shortMessage;
            objLogger["duration"] = timer.getElapsedMilliseconds();
            logger.error(objLogger, "Erro ao consultar a api de precificação ", error.message);

            throw new Error(error);

        }
    }

    /**
     * @method sendTray     
     */
    public async sendTray(apiToken: string, request: any) {
        request;
        const config: AxiosRequestConfig = {
            baseURL: this.BASE_URL,
            method: "POST",
            headers: {
                Authorization: 'Bearer ' + apiToken,
                "X-Api-Key": this.X_API_KEY
            },
            data: {
            }
        }

        this.printCurlCommand(config);

        const { data } = await Axios(config);
        return data;
    }


    /**
     * @method startBooking     
     */
    public async startBooking(apiToken: string, request: any) {
        request;
        const config: AxiosRequestConfig = {
            baseURL: this.BASE_URL + '/api/Booking/StartBooking',
            method: "POST",
            headers: {
                Authorization: 'Bearer ' + apiToken,
                "X-Api-Key": this.X_API_KEY
            },
            data: request
        }
        
        this.printCurlCommand(config);

        const { data } = await Axios(config);
        return data;
    }

    /**
     * @method makeBooking     
     */
    public async makeBooking(apiToken: string, request: any) {
        request;
        const config: AxiosRequestConfig = {
            baseURL: this.BASE_URL + '/api/Booking/MakeBooking',
            method: "POST",
            headers: {
                Authorization: 'Bearer ' + apiToken,
                "X-Api-Key": this.X_API_KEY
            },
            data: request
        }

        this.printCurlCommand(config);

        const { data } = await Axios(config);
        return data;
    }

    /**
     * @method getBooking     
     */
    public async getBooking(apiToken: string, bookingId: any) {
        const config: AxiosRequestConfig = {
            baseURL: this.BASE_URL + '/api/Booking/Detail/' + bookingId,
            method: "GET",
            headers: {
                Authorization: 'Bearer ' + apiToken,
                "X-Api-Key": this.X_API_KEY
            },
        }

        this.printCurlCommand(config);

        const { data } = await Axios(config);
        return data;
    }

    /**
     * @method cancelBooking     
     */
    public async cancelBooking(apiToken: string, request: any) {
        request;
        const config: AxiosRequestConfig = {
            baseURL: this.BASE_URL + '/api/Booking/CancelBooking',
            method: "POST",
            headers: {
                Authorization: 'Bearer ' + apiToken,
                "X-Api-Key": this.X_API_KEY
            },
            data: request
        }

        this.printCurlCommand(config);
        
        const { data } = await Axios(config);
        return data;
    }
}

