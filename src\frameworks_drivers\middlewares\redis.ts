import redis from 'redis'
import {promisify} from 'util';
import ConsulConsult from './consulConsult';
//import './../../env';

export default class Redis {    
    private client;
    private ttl = 345600;

    constructor() {
        this.client = null;
    }

    private async conect() {
        if (!this.client) {
        const consulConsult = new ConsulConsult();
        const redisUrl = await consulConsult.consultKey("redis_host");
        const redisPort = await consulConsult.consultKey("redis_port");
        this.client = redis.createClient(redisPort, redisUrl, { db: 4, tls:true });
        this.client.on("connect", function () {
            console.log("Connected to Redis");
        });
        this.client.on("error", function (error) {
            console.error("Redis connection error:", error);
        });
        }
    }

    public async setKeyRaw(key: any, dataRaw: any) {
        await this.conect();

        const data = JSON.stringify(dataRaw);

        await this.client.set(key, data);
    };
    
    public async setKey(key:any, data:any)
	{       
        await this.conect();
        await this.client.hmset
		(
			key, data,
			function ( error )
			{
				if ( error )
				{
					throw {
						message: "hmset error",
						error: error
					}
				}
			}
		);
        console.log('key:', key)
        setTimeout(() => this.client.expire(key, this.ttl), 10000)
		/*
        this.client.hgetall(key, function(err:any, object:any) {
            err;
            object;            
        });
		*/
    }

    public async getKeyRaw(key: any) {
        await this.conect();

        const getAsync = promisify(this.client.get).bind(this.client);   

        const value: any = await getAsync(key);

        return JSON.parse(value);
    }

    public async getKey(key:any){
        await this.conect();                
        const getAsync = promisify(this.client.hgetall).bind(this.client);        
        let value = await getAsync(key);        
        return value;
    }

    public async deleteField(key:any, field:any){
        await this.conect();                
        await this.client.hdel(field,key)        
    }
}
