apiVersion: apps/v1
kind: Deployment
metadata:
  name: corp-wl-vhs-bff-deploy
  namespace: corp-wl-vhs
  labels:
    app: corp-wl-vhs-bff
spec:
  replicas: 1
  selector:
    matchLabels:
      app: corp-wl-vhs-bff
  template:
    metadata:
      labels:
        app: corp-wl-vhs-bff
    spec:
      containers:
      - name: corp-wl-vhs-bff
        image: 260584439167.dkr.ecr.sa-east-1.amazonaws.com/corp-wl-vhs-bff:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 35
          periodSeconds: 15
        envFrom:
          - configMapRef:
              name: corp-wl-vhs-bff
          - secretRef:
              name: corp-wl-vhs-bff
        ports:
        - containerPort: 8080
        - containerPort: 5005
