export class StopWatch {
	private startTime = 0;

	private stopTime = 0;

	private running = false;

	public currentTime(): number {
		return new Date().getTime();
	}

	public start(): void {
		this.startTime = this.currentTime();
		this.running = true;
	}

	public stop(): void {
		this.stopTime = this.currentTime();
		this.running = false;
	}

	public getElapsedMilliseconds(): number {
		if (this.running) {
			this.stopTime = this.currentTime();
		}

		return this.stopTime - this.startTime;
	}

	public static sleep(duration: number): void {
		const now = new Date().getTime();

		while (new Date().getTime() < now + duration) { /* do nothing */ }
	}
}
