import Consul from 'consul';
import <PERSON><PERSON>  from 'bluebird';
//import './../../env';
export default class ConsulConsult
{

    public async consultKey( key:any )
	{
        const getTo = `cvc-wl-vhs/${ key }`;

        let options = 
		{
			host:process.env.CONSUL_URL, port:'8500', promisify:  this.fromCallback
		};

        let consul = new Consul( options );

        const response = await consul.kv.get( getTo ).catch( ( error ) => error );
		const goAfterValue = response && response[ 0 ] && response[0].Value || null;

		const resource = `${options.host}:${options.port}/${getTo}`

		if ( goAfterValue === null )
		{

			console.log
			( 
				"Consul: Pay attention that the returning value from <PERSON> is absent from: ",
				resource,
				"An exception is being thrown now"
			);

			throw (
				{
					name: "AbsentConsulValue",
					message: "Could not retrieve value from " + resource,
					response:
					{
						statusMessage: response && response[ 1 ] && response[ 1 ].statusMessage,
						statusCode: response && response[ 1 ] && response[ 1 ].statusCode,
						data: response && response[ 0 ]
					}
				}
			)

		}
		else
		{
			/*
			console.log
			( 
				"Consul: Successfully acquired value from", resource, "->", goAfterValue 
			);
			*/
		}
		return goAfterValue;

    }

    public fromCallback(fn:any)  
	{
        return new Bluebird
		(
			function(resolve, reject) 
			{
	            try 
				{
	                return fn
					(
						function(err:any, data:any, res:any) 
						{
		                    if (err) 
							{
				                err.res = res;
				                return reject(err);
			                }
			                return resolve([data, res]);
			            }
					);
	            } 
				catch (err) 
				{
		            return reject(err);
	            }
	        }
		);
    }

}
