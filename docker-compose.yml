services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VERACODE_APP_ID=9d3233cb10a90d3cc796f68bd7939d11
        - VERACODE_API_KEY=11b31b139d9be177d461ab99d809e643471ebdca0c37959d96a064dc58c0f4d18a9235fc8dac042992f24396d86658e67f1d9793e3416ca8d930958a90b785a8
        - BUILD_ID=vTeste
    image: corp-wl-vhs-bff
    ports:
      - '8080:8080'
    environment:
      - NODE_ENV=qa
      - NODE_DATASOURCE_ORACLE_USERNAME=BFF_VHS
      - NODE_DATASOURCE_ORACLE_PASSWORD=bffvhs2014cvc#
      - SPRING_DECRYPTY=wsftyuio095fr@15
